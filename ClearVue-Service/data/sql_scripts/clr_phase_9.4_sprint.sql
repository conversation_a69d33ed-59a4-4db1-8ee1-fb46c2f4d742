/* Migration script to add temp_to_perm_leaver column to workers table */
/* This column tracks if an inactive worker is a normal leaver or temp-to-perm conversion */

-- Add temp_to_perm_leaver column to workers table
ALTER TABLE `workers`
ADD COLUMN `temp_to_perm_leaver` TINYINT(1) NULL DEFAULT NULL
AFTER `inactivated_by`;

-- Add index for better performance when filtering by temp_to_perm_leaver
CREATE INDEX `idx_workers_temp_to_perm_leaver` ON `workers` (`temp_to_perm_leaver`);

-- Add index for combined filtering on is_active and temp_to_perm_leaver
CREATE INDEX `idx_workers_active_temp_to_perm` ON `workers` (`is_active`, `temp_to_perm_leaver`);

/*
Usage Notes:
- This field should only be populated for agency workers (where agency_id is not null)
- When is_active = false and this field = 1 (true), it indicates a temp-to-perm conversion
- When is_active = false and this field = 0 (false), it indicates a normal leaver
- When is_active = true, this field should remain NULL (worker is still active)
- NULL value means the worker is currently active (not a leaver)
*/

