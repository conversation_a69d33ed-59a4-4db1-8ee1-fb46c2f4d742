# Sample CSV Files Update for Temp-to-Perm Leaver Feature

## Overview

This document outlines the required updates to the agency worker sample CSV files to include the new `temp_to_perm_leaver` column.

## Files to Update

### 1. Agency Worker Upload Sample

**File**: `demo_agency_worker_data_to_upload.csv`
**S3 Location**: `workers-sample-csv/demo_agency_worker_data_to_upload.csv`

### 2. Agency Worker Update Sample

**File**: `demo_agency_worker_data_to_update.csv`
**S3 Location**: `workers-sample-csv/demo_agency_worker_data_to_update.csv`

## Required Changes

### New Column Addition

Add a new column `temp_to_perm_leaver` to both CSV files.

**Position**: Add as the last column in the CSV files.

**Column Header**: `temp_to_perm_leaver`

### Sample Data Examples

For **Upload CSV** (`demo_agency_worker_data_to_upload.csv`):

```csv
payroll_ref,employee_id,site,national_insurance_number,first_name,last_name,dob,nationality,sex,email,job_name,department_name,shift_name,role_type,start_date,assignment_date,is_active,inactivated_date,post_code,transport,other_assignment,pension_opt_out,house_number,limited_hours,student_visa,supervisor_status,sort_code,account_number,temp_to_perm_leaver
PR001,EMP001,Main Site,*********,John,Doe,15-01-1990,British,Male,<EMAIL>,Warehouse Operative,Warehouse,Day Shift,GENERAL,01-01-2024,01-01-2024,Yes,,SW1A 1AA,No,No,Yes,123,No,No,FRONT_OFFICE,123456,********,
PR002,EMP002,Main Site,CD789012E,Jane,Smith,22-03-1985,British,Female,<EMAIL>,Warehouse Operative,Warehouse,Day Shift,GENERAL,01-01-2024,01-01-2024,No,15-06-2024,SW1A 1BB,No,No,Yes,456,No,No,BACK_OFFICE,234567,********,Yes
PR003,EMP003,Main Site,EF345678G,Bob,Johnson,10-07-1992,British,Male,<EMAIL>,Warehouse Operative,Warehouse,Night Shift,GENERAL,01-02-2024,01-02-2024,No,20-06-2024,SW1A 1CC,Yes,Yes,No,789,Yes,No,,345678,********,No
```

For **Update CSV** (`demo_agency_worker_data_to_update.csv`):

```csv
payroll_ref,employee_id,site,first_name,last_name,national_insurance_number,dob,nationality,sex,job_name,department_name,shift_name,role_type,start_date,assignment_date,is_active,post_code,transport,other_assignment,pension_opt_out,house_number,student_visa,email,inactivated_date,supervisor_status,temp_to_perm_leaver
PR001,EMP001,Main Site,John,Doe,*********,15-01-1990,British,Male,Warehouse Operative,Warehouse,Day Shift,GENERAL,01-01-2024,01-01-2024,Yes,SW1A 1AA,No,No,Yes,123,No,<EMAIL>,,FRONT_OFFICE,
PR002,EMP002,Main Site,Jane,Smith,CD789012E,22-03-1985,British,Female,Warehouse Operative,Warehouse,Day Shift,GENERAL,01-01-2024,01-01-2024,No,SW1A 1BB,No,No,Yes,456,No,<EMAIL>,15-06-2024,BACK_OFFICE,Yes
PR003,EMP003,Main Site,Bob,Johnson,EF345678G,10-07-1992,British,Male,Warehouse Operative,Warehouse,Night Shift,GENERAL,01-02-2024,01-02-2024,No,SW1A 1CC,Yes,Yes,No,789,Yes,<EMAIL>,20-06-2024,,No
```

## Column Usage Guidelines

### When to Use `temp_to_perm_leaver`

- **For Active Workers (`is_active = Yes`)**: Leave this field empty/blank
- **For Normal Leavers (`is_active = No`)**: Enter `No` (or `0`, `false`, `FALSE`)
- **For Temp-to-Perm Conversions (`is_active = No`)**: Enter `Yes` (or `1`, `true`, `TRUE`)

### Accepted Values

- **Yes/True/1**: Indicates temp-to-perm conversion
- **No/False/0**: Indicates normal leaver
- **Empty/Blank**: Indicates active worker (not a leaver)

### Important Notes

1. This column is **only applicable for agency workers** (temporary workers)
2. The field is **optional** - leaving it empty indicates an active worker
3. Only populate this field when `is_active = No` (worker has left)
4. Use `Yes`/`1`/`true` for temp-to-perm conversions, `No`/`0`/`false` for normal leavers

## Deployment Steps

1. Update both CSV files with the new column
2. Upload the updated files to the respective S3 locations
3. Test the upload functionality with the new sample files
4. Update any documentation that references these sample files

## Testing

After updating the sample files:

1. Download the sample files from the application
2. Verify the new column is present
3. Test uploading workers with various `temp_to_perm_leaver` values
4. Verify the search and export functionality works with the new field
