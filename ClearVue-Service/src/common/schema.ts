/**
 * All the validation related Schema.
 */

const JoiBase = require('joi');
const JoiDateExtension = require('@joi/date');
const Joi = JoiBase.extend(JoiDateExtension);
import { dateTimeFormates, workerTableAllowedSortingFields, bookingListingAllowedFields, payrollDataAllowedSortingFields, agencyClientAssociationTableAllowedSortingFields, messageListAllowedSortingFields, payrollSummaryAllowedSortingFields, getAgenciesPaginationSchemaAllowedSortingFields, getClientsPaginationSchemaAllowedSortingFields, paginationSchemaWithClientIdAllowedSortingFields, dashboardPaginationSchemaAllowedSortingFields, complianceWorkersListAllowedSortingFields, supervisorsWeeklyDataAllowedSortingFields, supervisorWorkersDataAllowedSortingFields, getCreditDuesAllowedSortingFields, site, adjustmentWorkersDataAllowedSortingFields, agency } from "../common/constants";
import {
    MessageType, MessageBodyContentType, MessageReceiverGroups, WorkerSideMessagesType,
    WorkerSideMessagesScreenViewType, FaqUrlType, Languages, SchemaValidationErrorCode, WeekDays, ComplinaceCardsType, PayTypes, WorkerSupervisorStatus
} from "./enum"
import { customErrorMessageForSchemaValidationWithValue } from '../utils';
import { nationalitiesPossibleValues, sexPossibleValues, supervisorStatusPossibleValues } from '../common';

// Define the custom validation function to remove special characters
export const removeSpecialChars = (value) => {
    const input = value.replace(/[^a-zA-Z]/g, '').toLowerCase(); // Replace any non-alphabetic character with an empty string
    return nationalitiesPossibleValues[input] || null
};

export const UserLoginRequestSchema = Joi.object({
    email: Joi.string().email().lowercase().min(1).required(),
    password: Joi.string().min(8).error(new Error('Invalid email or password.')).required(),
}).options({ abortEarly: false, allowUnknown: true })

export const RenewAccessTokenRequestSchema = Joi.object({
    refresh_token: Joi.string().min(1).required(),
}).options({ abortEarly: false, allowUnknown: true })

// Define the custom validation function to return gender
export const sexValidation = (value) => {
    return sexPossibleValues[value] || null
};

const trimSpace = str => str ? str.replace(/\s/g, '') : null;

// Define the custom validation function to return Supervisor status
export const supervisorStatisValidation = (value) => {
    value = trimSpace(value);
    return supervisorStatusPossibleValues[value] || null;
};

export const csvValidationErrorHandler = (errors) => {
    errors.forEach((err) => {
        if (err.code === SchemaValidationErrorCode.UNKNOWN_COLUMN) {
            err.message = `Oops! CSV sheet contains unknown column: [ '${err.local.key}' ] `;
        }
        else if (err.code === SchemaValidationErrorCode.ALPHANUMERIC) {
            err.message = `Invalid ${err.local.key} value: [ '${err.local.value}' ] at row number: [ ${err.path[0] + 2} ]. \n\n It must only contain alpha-numeric characters.'`
        }
        else if (err.code === SchemaValidationErrorCode.INVALID || err.code === SchemaValidationErrorCode.REGEX || err.code === SchemaValidationErrorCode.EMAIL) {
            err.message = `Invalid ${err.local.key} value: [ '${err.local.value}' ] at row number: [ ${err.path[0] + 2} ]. \n\n Please check the value and try again or contact the admin.`;
        }
        else if (err.code === SchemaValidationErrorCode.DATE_FORMAT) {
            err.message = `Invalid '${err.local.key}' value: [ '${err.local.value}' ] at row number: [ ${err.path[0] + 2} ]. \n\n Valid Format: '${err.flags.format}'`;
        }
        else if (err.code === SchemaValidationErrorCode.MIN_LIMIT) {
            err.message = `Invalid '${err.local.key}' value: [ '${err.local.value}' ] at row number: [ ${err.path[0] + 2} ]. \n\n Field must contains at least ${err.local.limit} characters.`;
        }
        else if (err.code === SchemaValidationErrorCode.REQUIRED) {
            err.message = `Field '${err.local.key}' is Required. Not Found In Sheet.`;
        }
        else if (err.code === SchemaValidationErrorCode.ANY_ONLY) {
            err.message = `Invalid '${err.local.key}' value: [ '${err.local.value}' ] at row number: [ ${err.path[0] + 2} ]. \n\n Valid Inputs: [ '${err.local.valids}' ].`;
        }
        else if (err.code === SchemaValidationErrorCode.NUMBER_BASE) {
            err.message = `Invalid '${err.local.key}' at row number: [ ${err.path[0] + 2} ]. \n\n It must only contain number'`;
        }
        else if (err.code === SchemaValidationErrorCode.CUSTOM_BANKDETAILS) {
            err.local.key = "sort_code or account_number";
            err.message = `Missing '${err.local.key}' at row number: [ ${err.path[0] + 2} ]. \n\n Both must either be empty or both must have values together.'`;
        }
        else if (err.code === SchemaValidationErrorCode.STUDENT_VISA_LIMITED_HOURS) {
            err.message = `If student_visa is 'Yes' then limited_hours must also be 'Yes' at row number: [ ${err.path[0] + 2} ].`;
        }
        else {
            err.local.label = `[ ${err.path[0] + 2} ].[ ${err.local.key} ].[ ${err.local.value} ] `
        }
    });
    return errors;
};

// Languages without "en" code (as translation object should not have "english" language inside) | (Tigrinya Not allowed yet from WebApp)
const languagesCode = Joi.object({
    bg: Joi.string().required(),
    es: Joi.string().required(),
    et: Joi.string().required(),
    cs: Joi.string().required(),
    fr: Joi.string().required(),
    hi: Joi.string().required(),
    hu: Joi.string().required(),
    it: Joi.string().required(),
    lt: Joi.string().required(),
    lv: Joi.string().required(),
    ne: Joi.string().required(),
    pl: Joi.string().required(),
    pt: Joi.string().required(),
    ro: Joi.string().required(),
    sk: Joi.string().required(),
    sl: Joi.string().required(),
    sq: Joi.string().required(),
    uk: Joi.string().required(),
    ur: Joi.string().required(),
});

export const AddNewClientSchema = Joi.object({
    name: Joi.string().min(1).required(),
    address_line_1: Joi.string().min(1).required(),
    address_line_2: Joi.string().allow(null, ''),
    address_line_3: Joi.string().allow(null, ''),
    city: Joi.string().min(1).pattern(/^[aA-zZ\s]+$/).error(new Error('Only alphabets are allowed for the field city.')).required(),
    country: Joi.string().min(1).required(),
    sectorId: Joi.number().min(1).required(),
    postCode: Joi.string().min(1).allow(null, ''),
    weekday_start: Joi.string().min(1).required().valid(
        WeekDays.SUNDAY,
        WeekDays.MONDAY,
        WeekDays.TUESDAY,
        WeekDays.WEDNESDAY,
        WeekDays.THURSDAY,
        WeekDays.FRIDAY,
        WeekDays.SATURDAY
    ),
    booking_format: Joi.string().valid('HOURS', 'HEADS').required(),
    worker_invite_email: Joi.boolean().strict().valid(true, false).default(false),
    workerPerformance: Joi.boolean().strict().valid(true, false).default(false).required(),
    workerTraining: Joi.boolean().strict().valid(true, false).default(false).required(),
    rateCardLookup: Joi.boolean().strict().valid(true, false).default(false).required(),
    tna_ftp: Joi.boolean().valid(true, false).required(),
    worker_ftp: Joi.boolean().valid(true, false).required(),

    // FTP related fields with conditional logic
    ftp_host: Joi.alternatives().conditional(Joi.ref('tna_ftp'), {
        is: true,
        then: Joi.string().min(1).required(),
        otherwise: Joi.alternatives().conditional(Joi.ref('worker_ftp'), {
            is: true,
            then: Joi.string().min(1).required(),
            otherwise: Joi.string().allow(null, '')
        })
    }),

    ftp_username: Joi.alternatives().conditional(Joi.ref('tna_ftp'), {
        is: true,
        then: Joi.string().min(1).required(),
        otherwise: Joi.alternatives().conditional(Joi.ref('worker_ftp'), {
            is: true,
            then: Joi.string().min(1).required(),
            otherwise: Joi.string().allow(null, '')
        })
    }),

    ftp_password: Joi.alternatives().conditional(Joi.ref('tna_ftp'), {
        is: true,
        then: Joi.string().min(1).required(),
        otherwise: Joi.alternatives().conditional(Joi.ref('worker_ftp'), {
            is: true,
            then: Joi.string().min(1).required(),
            otherwise: Joi.string().allow(null, '')
        })
    }),

    tna_cron_expression: Joi.string().min(1).when('tna_ftp', {
        is: true,
        then: Joi.required(),
        otherwise: Joi.allow(null, '')
    }),

    worker_cron_expression: Joi.string().min(1).when('worker_ftp', {
        is: true,
        then: Joi.required(),
        otherwise: Joi.allow(null, '')
    }),

    remote_directory: Joi.alternatives().conditional(Joi.ref('tna_ftp'), {
        is: true,
        then: Joi.string().min(1).required(),
        otherwise: Joi.alternatives().conditional(Joi.ref('worker_ftp'), {
            is: true,
            then: Joi.string().min(1).required(),
            otherwise: Joi.string().allow(null, '')
        })
    }),

    notification_email: Joi.alternatives().conditional(Joi.ref('tna_ftp'), {
        is: true,
        then: Joi.string().email().min(1).required(),
        otherwise: Joi.alternatives().conditional(Joi.ref('worker_ftp'), {
            is: true,
            then: Joi.string().email().min(1).required(),
            otherwise: Joi.string().allow(null, '')
        })
    }),
}).options({ abortEarly: false, allowUnknown: false })

export const setTrainingRule = Joi.object({
    site_id: Joi.number().min(1).required(),
    region_id: Joi.number().min(1).required(),
    threshold_week: Joi.number().min(0).max(53).required(),
    evaluation_week: Joi.number().min(0).max(53).required(),
    limited_hours_threshold_week: Joi.number().min(0).max(53).allow(null, '').required(),
    limited_hours_evaluation_week: Joi.number().min(0).max(53).allow(null, '').required(),
    max_training_hours: Joi.number().min(0).max(999).required(),
    performance_threshold: Joi.number().min(0).max(999).required(),
    credit_rate: Joi.number().min(0).max(100).required(),
}).custom((value, helpers) => {
    // Check if both limitedHours fields are empty or both have values
    const islimitedHoursThresholdEmpty = value.limited_hours_threshold_week === null || value.limited_hours_threshold_week === '';
    const islimitedHoursEvaluationEmpty = value.limited_hours_evaluation_week === null || value.limited_hours_evaluation_week === '';

    if (islimitedHoursThresholdEmpty !== islimitedHoursEvaluationEmpty) {
        return helpers.message('Both "Limited hours threshold week" and "Limited hours evaluation week" must either both be empty or both have values');
    }
    return value;
}).options({ abortEarly: false, allowUnknown: false });

export const setStartDateYearlyRule = Joi.object({
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    total_weeks: Joi.number().min(1).max(53).required()
}).options({ abortEarly: false, allowUnknown: false });

export const setFinancialRule = Joi.object({
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    total_weeks: Joi.number().min(1).required(),
    ni_percent: Joi.number().min(0).max(100).required(),
    ni_threshold: Joi.number().min(0).max(999).required(),
    pension_percent: Joi.number().min(0).max(100).required(),
    pension_threshold: Joi.number().min(0).max(999).required(),
    app_levy_percent: Joi.number().min(0).max(100).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const ForgotPasswordRequestSchema = Joi.object({
    email: Joi.string().email().lowercase().min(1).required(),
}).options({ abortEarly: false, allowUnknown: true })

export const AddAndUpdateRegionSchema = Joi.object({
    name: Joi.string().min(1).required(),
    client_id: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: true })

export const timeAndAttendanceIdsSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1).required(),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    week: Joi.number().required()
}).options({ abortEarly: false, allowUnknown: true })

export const ftpTimeAndAttendanceIdsSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1).required(),
    date_of_upload_week: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required()
}).options({ abortEarly: false, allowUnknown: true })

export const totalAgencyPaySchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1).required(),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required()
}).options({ abortEarly: false, allowUnknown: true })

export const ResetPasswordRequestSchema = Joi.object({
    code: Joi.string().min(1).required(),
    password: Joi.string().min(8).pattern(/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[^\w\s])(?!.*\s).{8,}$/).error(new Error('The password should be at least 8 characters long and must include at least one uppercase letter, one lowercase letter, one number, and one special character.')).required(),
}).options({ abortEarly: false, allowUnknown: true })

export const CreateAgencyRequestSchema = Joi.object({
    name: Joi.string().required(),
    address_line_1: Joi.string().required(),
    address_line_2: Joi.string().allow(null, ''),
    address_line_3: Joi.string().allow(null, ''),
    city: Joi.string().pattern(/^[aA-zZ\s]+$/).error(new Error('Only alphabets are allowed for the field city.')).required(),
    country: Joi.string().required(),
    postCode: Joi.string().min(1).allow(null, '')
})

const commonClientSchemaFields = {
    name: Joi.string().required(),
    address_line_1: Joi.string().required(),
    address_line_2: Joi.string().allow(null, ''),
    address_line_3: Joi.string().allow(null, ''),
    city: Joi.string().pattern(/^[aA-zZ\s]+$/).error(new Error('Only alphabets are allowed for the field city.')).required(),
    country: Joi.string().required(),
    sectorId: Joi.number().min(1).required(),
    postCode: Joi.string().min(1).allow(null, ''),
    profile: Joi.string().allow(null, ''),
    client_id: Joi.number().min(1).required(),
};

export const UpdateClientSchemaForClientAdmin = Joi.object({
    ...commonClientSchemaFields
});


export const UpdateClientSchema = Joi.object({
    ...commonClientSchemaFields,
    workerPerformance: Joi.boolean().strict().valid(true, false),
    workerTraining: Joi.boolean().strict().valid(true, false),
    rateCardLookup: Joi.boolean().strict().valid(true, false),
    tna_ftp: Joi.boolean().valid(true, false).required(),
    worker_ftp: Joi.boolean().valid(true, false).required(),
    // FTP related fields with conditional logic
    ftp_host: Joi.alternatives().conditional(Joi.ref('tna_ftp'), {
        is: true,
        then: Joi.string().min(1).required(),
        otherwise: Joi.alternatives().conditional(Joi.ref('worker_ftp'), {
            is: true,
            then: Joi.string().min(1).required(),
            otherwise: Joi.string().allow(null, '')
        })
    }),

    ftp_username: Joi.alternatives().conditional(Joi.ref('tna_ftp'), {
        is: true,
        then: Joi.string().min(1).required(),
        otherwise: Joi.alternatives().conditional(Joi.ref('worker_ftp'), {
            is: true,
            then: Joi.string().min(1).required(),
            otherwise: Joi.string().allow(null, '')
        })
    }),

    ftp_password: Joi.alternatives().conditional(Joi.ref('tna_ftp'), {
        is: true,
        then: Joi.string().min(1).required(),
        otherwise: Joi.alternatives().conditional(Joi.ref('worker_ftp'), {
            is: true,
            then: Joi.string().min(1).required(),
            otherwise: Joi.string().allow(null, '')
        })
    }),

    tna_cron_expression: Joi.string().min(1).when('tna_ftp', {
        is: true,
        then: Joi.required(),
        otherwise: Joi.allow(null, '')
    }),

    worker_cron_expression: Joi.string().min(1).when('worker_ftp', {
        is: true,
        then: Joi.required(),
        otherwise: Joi.allow(null, '')
    }),

    remote_directory: Joi.alternatives().conditional(Joi.ref('tna_ftp'), {
        is: true,
        then: Joi.string().min(1).required(),
        otherwise: Joi.alternatives().conditional(Joi.ref('worker_ftp'), {
            is: true,
            then: Joi.string().min(1).required(),
            otherwise: Joi.string().allow(null, '')
        })
    }),

    notification_email: Joi.alternatives().conditional(Joi.ref('tna_ftp'), {
        is: true,
        then: Joi.string().email().min(1).required(),
        otherwise: Joi.alternatives().conditional(Joi.ref('worker_ftp'), {
            is: true,
            then: Joi.string().email().min(1).required(),
            otherwise: Joi.string().allow(null, '')
        })
    }),
}).options({ abortEarly: false, allowUnknown: false })

export const CreateAgencyAssociationRequestSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1).required(),
    margin: Joi.number().max(9.99).required(),
    currency: Joi.string(),
    overtime_margin: Joi.number().max(9.99).allow(null, ''),
    transport_fee: Joi.number().max(9.99).allow(null, ''),
    ssp: Joi.number().max(9.99).allow(null, ''),
    training_margin: Joi.number().max(9.99).allow(null, ''),
    induction_training_margin: Joi.number().max(9.99).allow(null, ''),
    bh_margin: Joi.number().max(9.99).allow(null, ''),
    nsp_margin: Joi.number().max(9.99).allow(null, ''),
    supervisor_standard_margin: Joi.number().max(9.99).allow(null, ''),
    suspension_margin: Joi.number().max(9.99).allow(null, ''),
    supervisor_overtime_margin: Joi.number().max(9.99).allow(null, ''),
    supervisor_permanent_margin: Joi.number().max(9.99).allow(null, ''),
    holiday_activation: Joi.boolean().default(false).strict().valid(true, false),
    holiday_cost_removed: Joi.boolean().default(false).strict().valid(true, false),
})

export const UpdateAgencyAssociationRequestSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1).required(),
    margin: Joi.number().max(9.99).required(),
    currency: Joi.string(),
    overtime_margin: Joi.number().max(9.99).allow(null, ''),
    transport_fee: Joi.number().max(9.99).allow(null, ''),
    ssp: Joi.number().max(9.99).allow(null, ''),
    induction_training_margin: Joi.number().max(9.99).allow(null, ''),
    training_margin: Joi.number().max(9.99).allow(null, ''),
    bh_margin: Joi.number().max(9.99).allow(null, ''),
    nsp_margin: Joi.number().max(9.99).allow(null, ''),
    supervisor_standard_margin: Joi.number().max(9.99).allow(null, ''),
    suspension_margin: Joi.number().max(9.99).allow(null, ''),
    supervisor_overtime_margin: Joi.number().max(9.99).allow(null, ''),
    supervisor_permanent_margin: Joi.number().max(9.99).allow(null, ''),
})

export const UpdateAgencyRequestSchema = Joi.object({
    name: Joi.string().required(),
    address_line_1: Joi.string().required(),
    address_line_2: Joi.string().allow(null, ''),
    address_line_3: Joi.string().allow(null, ''),
    city: Joi.string().pattern(/^[aA-zZ\s]+$/).error(new Error('Only alphabets are allowed for the field city.')).required(),
    country: Joi.string().required(),
    postCode: Joi.string().min(1).allow(null, ''),
    profile: Joi.string().allow(null, ''),
})

export const QueryParamsSchemaWithIdOnly = Joi.object({
    id: Joi.number().min(1).required()
})

const languageCodes = Joi.string().min(1).valid(
    Languages.ALBANIAN,
    Languages.BULGARIAN,
    Languages.CZECH,
    Languages.ENGLISH,
    Languages.ESTONIAN,
    Languages.FRENCH,
    Languages.HINDI,
    Languages.HUNGARIAN,
    Languages.ITALIAN,
    Languages.LATVIAN,
    Languages.LITHUANIAN,
    Languages.NEPALI,
    Languages.URDU,
    Languages.UKRAINIAN,
    Languages.POLISH,
    Languages.PORTUGUESE,
    Languages.ROMANIAN,
    Languages.SLOVAK,
    Languages.SLOVANIAN,
    Languages.SPANISH,
    Languages.TIGRINYA
);

export const QueryParamsSchemaWithLanguageOnly = Joi.object({
    language: languageCodes.default(Languages.ENGLISH)
})

export const GetNationalityQueryParamsSchema = Joi.object({
    site_id: Joi.number().min(1),
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1),
    type: Joi.string().valid('TEMPORARY', 'PERMANENT')
})

export const QueryParamsForSurveyAnalysis = Joi.object({
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1),
    site_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    shift_id: Joi.number().min(1),
    department_id: Joi.number().min(1),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw(),
    type: Joi.string().valid('TEMPORARY', 'PERMANENT')
})

export const CreateSurveySchema = Joi.object({
    result: Joi.array().items(Joi.object({
        workerId: Joi.number().required(),
        questionId: Joi.number().min(1).required(),
        siteId: Joi.number().min(1).required(),
        agencyId: Joi.number().min(1).allow(null, ''),
        clientId: Joi.number().min(1).required(),
        surveyId: Joi.number().min(1).required(),
        rating: Joi.string().valid(null, "0.5", "1.0", "1.5", "2.0", "2.5", "3.0", "3.5", "4.0", "4.5", "5.0"),
        answers: Joi.array().items(Joi.object({
            id: Joi.number(),
            answer: Joi.string()
        }))
    }))
})

export const QueryParamsForPayrollSummary = Joi.object({
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    site_id: Joi.number().min(1),
    payroll_meta_ids: Joi.array().items(Joi.number().min(1)),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw(),
    page: Joi.number().required(),
    limit: Joi.number().required(),
    sort_by: Joi.string().lowercase().valid(...payrollSummaryAllowedSortingFields),
    sort_type: Joi.string().valid("ASC", "DESC")
})

export const QueryParamsForPayrollInvoice = Joi.object({
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    site_id: Joi.number().min(1),
    payroll_meta_ids: Joi.array().items(Joi.number().min(1)),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw()
})

export const QueryParamsForCreditDues = Joi.object({
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    site_id: Joi.number().min(1),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw(),
    page: Joi.number().min(1).required(),
    limit: Joi.number().min(1).required(),
    sort_by: Joi.string().valid(...getCreditDuesAllowedSortingFields),
    sort_type: Joi.string().valid("ASC", "DESC"),
    where_rate_is: Joi.number().min(0)
}).options({ abortEarly: false, allowUnknown: false });

export const QueryParamsForSupervisorsWeeklyData = Joi.object({
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    site_id: Joi.number().min(1),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw(),
    page: Joi.number().required(),
    limit: Joi.number().required(),
    sort_by: Joi.string().valid(...supervisorsWeeklyDataAllowedSortingFields),
    sort_type: Joi.string().valid("ASC", "DESC")
})

export const QueryParamsForSupervisorWorkersData = Joi.object({
    page: Joi.number().required(),
    limit: Joi.number().required(),
    sort_by: Joi.string().valid(...supervisorWorkersDataAllowedSortingFields),
    sort_type: Joi.string().valid("ASC", "DESC")
})

export const GetAgenciesPaginationSchema = Joi.object({
    page: Joi.number().required(),
    limit: Joi.number().required(),
    sort_by: Joi.string().valid(...getAgenciesPaginationSchemaAllowedSortingFields),
    sort_type: Joi.string().valid("ASC", "DESC")
})

export const GetClientsPaginationSchema = Joi.object({
    page: Joi.number().required(),
    limit: Joi.number().required(),
    sort_by: Joi.string().valid(...getClientsPaginationSchemaAllowedSortingFields),
    sort_type: Joi.string().valid("ASC", "DESC")
})

export const DashboardPaginationSchema = Joi.object({
    page: Joi.number().required(),
    limit: Joi.number().required(),
    sort_by: Joi.string().valid(...dashboardPaginationSchemaAllowedSortingFields),
    sort_type: Joi.string().valid("ASC", "DESC")
})


export const departmentPaginationSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    page: Joi.number().min(1),
    limit: Joi.number().min(1)
})

export const dropDownSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1),
    region_id: Joi.number().min(1)
})

export const PaginationSchemaWithClientId = Joi.object({
    client_id: Joi.number().allow(null, ''),
    site_id: Joi.number().min(1).allow(null, ''),
    region_id: Joi.number().min(1).allow(null, ''),
    page: Joi.number().required(),
    limit: Joi.number().required(),
    sort_by: Joi.string().valid(...paginationSchemaWithClientIdAllowedSortingFields),
    sort_type: Joi.string().valid("ASC", "DESC")
})


export const siteIdQueryParams = Joi.object({
    site_id: Joi.number().min(1).required(),
});

export const CreateAndUpdateDepartmentRequestSchema = Joi.object({
    name: Joi.string().min(1).required(),
    client_id: Joi.number().min(1).required(),
    cost_centre: Joi.string().allow(null, ''),
    associations: Joi.array().items(
        Joi.object({
            site_id: Joi.number().required(),
            region_id: Joi.number(),
            region_name: Joi.string(),
            site_name: Joi.string(),
        })
    ).required(),
})
export const CreateAndUpdateSectorRequestSchema = Joi.object({
    key: Joi.string().required(),
    value: Joi.string().required()
})

export const CreateJobRequestSchema = Joi.object({
    name: Joi.string().required(),
    clientId: Joi.number().min(1),
    departmentId: Joi.array().items(Joi.number()),
    siteId: Joi.number().min(1),
    type: Joi.string(),
    shiftId: Joi.number().min(1),
    hoursPerWeek: Joi.number(),
})
export const UpdateJobRequestSchema = Joi.object({
    name: Joi.string().required(),
    clientId: Joi.number().min(1).required(),
    departmentId: Joi.array().items(Joi.number()),
    siteId: Joi.number().min(1),
    type: Joi.string().required(),
    shiftId: Joi.number().min(1),
    hoursPerWeek: Joi.number(),
})

export const addAndUpdateSiteSchema = Joi.object({
    name: Joi.string().min(1).required(),
    region_id: Joi.number().min(1).required(),
    address_line_1: Joi.string().required(),
    address_line_2: Joi.string().allow(null, ''),
    address_line_3: Joi.string().allow(null, ''),
    cost_centre: Joi.string().allow(null, ''),
    post_code: Joi.string().regex(/^[A-Za-z0-9 ]*$/).uppercase().allow(null, ''),
    city: Joi.string().pattern(/^[aA-zZ\s]+$/).error(new Error('Only alphabets are allowed for the field city.')).required(),
    country: Joi.string().required(),
    client_id: Joi.number().min(1).required(),
    wtr: Joi.array().items(
        Joi.object({
            rule_name: Joi.string().required(),
            role_type: Joi.string().required(),
            start_tax_year: Joi.date().iso().required(),
            pay_type: Joi.string().required().valid(
                PayTypes.STANDARD,
                PayTypes.OVERTIME,
                PayTypes.WEEKEND,
                PayTypes.BH,
                PayTypes.SUSPENSION,
                PayTypes.INTERNAL_STANDARD,
                PayTypes.SUPERVISOR_STANDARD,
                PayTypes.NSP,
                PayTypes.INDUCTION_TRAINING,
                PayTypes.TRAINING,
                PayTypes.SP,
                PayTypes.STANDARD_BONUS,
                PayTypes.SPECIAL_BONUS,
                PayTypes.INTERNAL_OVERTIME,
                PayTypes.INTERNAL_PERMANENT,
                PayTypes.SUPERVISOR_OVERTIME,
                PayTypes.SUPERVISOR_PERMANENT
            ).insensitive(),
            pre_twelve_week: Joi.number().required(),
            post_twelve_week: Joi.number().required(),
            los: Joi.object().allow(null, ''),
        })
    ).error((errors) => {
        errors.forEach((err) => {
            if (err.code === SchemaValidationErrorCode.ANY_ONLY) {
                err.message = `Invalid '${err.local.key}' value: [ '${err.local.value}' ] in Holiday (WTR) Ruling. \n at [ ${err.state.ancestors[0]?.rule_name || 'ROW: ' + (err.path[1] + 1)} ]. \n\n Valid Inputs: [ '${err.local.valids}' ].`;
            }
        });
        return errors;
    }),
}).options({ abortEarly: false, allowUnknown: true });

export const AddWorkerSchema = Joi.object({
    first_name: Joi.string().min(1).allow(null, ''),
    last_name: Joi.string().min(1).allow(null, ''),
    date_of_birth: Joi.date().max('now').format(dateTimeFormates.YYYYMMDD).raw().required(),
    national_insurance_number: Joi.string().min(1).allow(null, ''),
    post_code: Joi.string().regex(/^[A-Za-z0-9 ]*$/).uppercase().allow(null, ''),
    nationality: Joi.string().min(1).regex(/^[\w,() ]*$/).custom((value, helpers) => {
        const processedValue = removeSpecialChars(value);
        if (!processedValue) {
            return helpers.error(SchemaValidationErrorCode.INVALID);
        }
        return processedValue;
    }).error((errors) => {
        errors.forEach((err) => {
            if (err.code === SchemaValidationErrorCode.INVALID || err.code === SchemaValidationErrorCode.REGEX) {
                err.message = `Invalid Nationality Value: [ ${err.local.value} ]. \n\n Please check the spelling try again or contact the admin.`;
            }
        });
        return errors;
    }).allow(null, '').required(),

    orientation: Joi.string().lowercase().custom((value, helpers) => {
        const newValue = sexValidation(value);
        if (!newValue) return helpers.error(SchemaValidationErrorCode.INVALID);
        else return newValue;
    }).error((errors) => {
        errors.forEach((err) => {
            if (err.code === SchemaValidationErrorCode.INVALID) {
                err.message = `Invalid Orientation/Sex field Value: [ ${err.local.value} ]. \n\n It must be one of ['m', 'male', 'Male', 'f', 'female', 'Female', 'o', 'other', 'Other']`;
            }
        });
        return errors;
    }).allow("", null),

    email: Joi.string().email({ minDomainSegments: 2 }).lowercase().min(1).error((errors) => {
        errors.forEach((err) => {
            if (err.code === "string.email") {
                err.message = `Invalid email value: '${err.local.value}'`;
            }
        });
        return errors;
    }).required(),
    country_code: Joi.string().pattern(/\+[0-9]+/).error(new Error('Please enter a valid country code.')).allow(null, ''),
    mobile: Joi.string().min(1).pattern(/^[0-9]+$/).allow(null, ""),
    payroll_ref: Joi.string().regex(/^[a-zA-Z0-9/-]+$/).required(),
    employee_id: Joi.string().regex(/^[a-zA-Z0-9/-]+$/).required(),
    job_id: Joi.number().min(1).required(),
    client_id: Joi.number().min(1).allow(null),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    assignment_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    agency_id: Joi.number().min(1).allow(null),
    is_active: Joi.boolean().default(true),
    pension_opt_out: Joi.boolean().default(true).required(),
    transport: Joi.boolean().default(false),
    other_assignment: Joi.boolean().default(false),
    limited_hours: Joi.boolean().default(false).required(),
    type: Joi.string().valid('TEMPORARY', 'PERMANENT').default('TEMPORARY'),
    student_visa: Joi.boolean().default(false).required(),
    house_number: Joi.string().min(1).allow(null, ''),
    workers_supervisor_status: Joi.string().valid(WorkerSupervisorStatus.FRONT_OFFICE, WorkerSupervisorStatus.BACK_OFFICE).default(null).allow(null, '').required(),
    sort_code: Joi.string().length(6).pattern(/^[0-9]+$/).allow(null, ''),
    account_number: Joi.string().min(8).max(15).pattern(/^[0-9]+$/).allow(null, ''),
}).custom((obj, helpers) => {
    if (obj.student_visa === true && obj.limited_hours !== true) {
        return helpers.message('If "Student Visa" is set to Yes, "Limited Hours" must also be set to Yes');
    }
    return obj;
}).options({ abortEarly: true, allowUnknown: true });

export const updateSingleWorkerSchema = Joi.object({
    first_name: Joi.string().min(1).allow(null, ''),
    last_name: Joi.string().min(1).allow(null, ''),
    email: Joi.string().email({ minDomainSegments: 2 }).lowercase().min(1).error((errors) => {
        errors.forEach((err) => {
            if (err.code === "string.email") {
                err.message = `Invalid email value: '${err.local.value}'`;
            }
        });
        return errors;
    }).required(),
    date_of_birth: Joi.date().max('now').format(dateTimeFormates.YYYYMMDD).raw().required(),
    post_code: Joi.string().regex(/^[A-Za-z0-9 ]*$/).uppercase().allow(null, ''),
    nationality: Joi.string().min(1).regex(/^[\w,() ]*$/).custom((value, helpers) => {
        const processedValue = removeSpecialChars(value);
        if (!processedValue) {
            return helpers.error(SchemaValidationErrorCode.INVALID);
        }
        return processedValue;
    }).error((errors) => {
        errors.forEach((err) => {
            if (err.code === SchemaValidationErrorCode.INVALID || err.code === SchemaValidationErrorCode.REGEX) {
                err.message = `Invalid Nationality Value: [ ${err.local.value} ]. \n\n Please check the spelling try again or contact the admin.`;
            }
        });
        return errors;
    }).allow(null, ''),
    orientation: Joi.string().lowercase().custom((value, helpers) => {
        const newValue = sexValidation(value);
        if (!newValue) return helpers.error(SchemaValidationErrorCode.INVALID);
        else return newValue;
    }).error((errors) => {
        errors.forEach((err) => {
            if (err.code === SchemaValidationErrorCode.INVALID) {
                err.message = `Invalid Orientation/Sex field Value: [ ${err.local.value} ]. \n\n It must be one of ['m', 'male', 'Male', 'f', 'female', 'Female', 'o', 'other', 'Other']`;
            }
        });
        return errors;
    }).allow("", null),
    country_code: Joi.string().pattern(/\+[0-9]+/).error(new Error('Please enter a valid country code.')).allow(null, ''),
    mobile: Joi.string().min(1).pattern(/^[0-9]+$/).allow(null, ''),
    payroll_ref: Joi.string().regex(/^[a-zA-Z0-9/-]+$/).required().allow(null, ''),
    employee_id: Joi.string().regex(/^[a-zA-Z0-9/-]+$/).required().allow(null, ''),
    job_id: Joi.number().min(1).required(),
    client_id: Joi.number().min(1).allow(null),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    assignment_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    agency_id: Joi.number().min(1).allow(null),
    is_active: Joi.boolean(),
    pension_opt_out: Joi.boolean().required(),
    national_insurance_number: Joi.string().min(1).allow(null, ''),
    documents: Joi.object().keys({
        passport: Joi.string().uri().min(1).allow(null, '').required(),
        driving_license: Joi.string().uri().allow(null, '').required(),
        identity_card: Joi.string().uri().allow(null, '').required(),
        utility_bill: Joi.string().uri().allow(null, '').required()
    }).allow(null, ''),
    transport: Joi.boolean(),
    other_assignment: Joi.boolean(),
    limited_hours: Joi.boolean().required(),
    student_visa: Joi.boolean().required(),
    house_number: Joi.string().min(1).allow(null, ''),
    workers_supervisor_status: Joi.string().valid(WorkerSupervisorStatus.FRONT_OFFICE, WorkerSupervisorStatus.BACK_OFFICE).allow(null, ''),
    sort_code: Joi.string().length(6).pattern(/^[0-9]+$/).allow(null, ''),
    account_number: Joi.string().min(8).max(15).pattern(/^[0-9]+$/).allow(null, ''),
}).custom((obj, helpers) => {
    if (obj.student_visa === true && obj.limited_hours !== true) {
        return helpers.message('If "Student Visa" is set to Yes, "Limited Hours" must also be set to Yes');
    }
    return obj;
}).options({ abortEarly: false, allowUnknown: true })

export const CreateRateCardRequestSchema = Joi.object({
    pay: Joi.number().min(0).required(),
    charge: Joi.number().min(0).required(),
    performance_low: Joi.number().min(0).max(999).allow(null, "").required(),
    performance_high: Joi.number().min(1).max(999).allow(null, "").required(),
    supervisor_rate: Joi.string().lowercase().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow(null, "").required(),
    pay_type: Joi.string().required().valid(
        PayTypes.STANDARD,
        PayTypes.OVERTIME,
        PayTypes.WEEKEND,
        PayTypes.BH,
        PayTypes.SUSPENSION,
        PayTypes.INTERNAL_STANDARD,
        PayTypes.SUPERVISOR_STANDARD,
        PayTypes.NSP,
        PayTypes.INDUCTION_TRAINING,
        PayTypes.TRAINING,
        PayTypes.SP,
        PayTypes.STANDARD_BONUS,
        PayTypes.SPECIAL_BONUS,
        PayTypes.INTERNAL_OVERTIME,
        PayTypes.INTERNAL_PERMANENT,
        PayTypes.SUPERVISOR_OVERTIME,
        PayTypes.SUPERVISOR_PERMANENT,
        PayTypes.HOLIDAY
    ).insensitive()
}).error(csvValidationErrorHandler);

export const rateCardBodySchema = Joi.object({
    name: Joi.string().required(),
    site_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false })

export const AddNewUserSchema = Joi.object({
    user_type: Joi.number().required(),
    users: Joi.array().min(1).items(Joi.object({
        id: Joi.number().min(1),
        name: Joi.string().required(),
        email: Joi.string().email().lowercase().required(),
        country_code: Joi.string().pattern(/\+[0-9]+/).error(new Error('Please enter a valid country code.')).allow(null, ''),
        phone: Joi.number().min(1).max(9999999999999).allow(null, "")
    })).required(),
})

export const UpdateWorkerSchema = Joi.object({
    client_id: Joi.number().min(1).allow(null),
    agency_id: Joi.number().min(1).allow(null),
    is_active: Joi.boolean().allow(null),
    job_id: Joi.number().min(1).allow(null),
    workers: Joi.array().items(Joi.number().min(1).required()).required()
})

export const GetWorkersListSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1),
    site_id: Joi.number().min(1),
    page: Joi.number().min(1),
    limit: Joi.number().min(0),
    sort_by: Joi.string().valid(...workerTableAllowedSortingFields),
    sort_type: Joi.string().valid("ASC", "DESC"),
    type: Joi.string().valid('TEMPORARY', 'PERMANENT')
}).options({ abortEarly: false, allowUnknown: true })

export const searchWorkersBodySchema = Joi.object({
    first_name: Joi.string().allow(null, ""),
    last_name: Joi.string().allow(null, ""),
    employee_id: Joi.string().allow(null, ""),
    payroll_ref: Joi.string().allow(null, ""),
    pension_opt_out: Joi.string().lowercase().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow(null, "").required(),
    national_insurance_number: Joi.string().allow(null, ""),
    email: Joi.string().lowercase().allow(null, ""),
    under_twentyone: Joi.boolean().allow(null, ""),
    under_twentytwo: Joi.boolean().allow(null, ""),
    within_twelveweeks: Joi.boolean().allow(null, ""),
    app_downloaded: Joi.string().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow(null, ""),
    all_workers: Joi.boolean().allow(null, ""),
    student_visa: Joi.string().lowercase().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow(null, ""),
    assignment_date: Joi.date().format(dateTimeFormates.YYYYMMDD).allow(null, ""),
    site_id: Joi.number().min(1).allow(null, ""),
    role_id: Joi.number().min(1).allow(null, ""),
    supervisor_status: Joi.string().valid('BACK_OFFICE', 'FRONT_OFFICE', 'BOTH').allow(null, ""),
    los: Joi.string().allow(null, ""),
    returning_worker: Joi.boolean().allow(null, ""),
    other_assignment: Joi.string().lowercase().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow(null, ""),
    banking_details: Joi.string().lowercase().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow(null, ""),
    limited_hours: Joi.string().lowercase().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow(null, ""),
    temp_to_perm_leaver: Joi.string().allow(null, "").description('Filter for temp-to-perm leavers. Use specific value to filter, empty to include all.'),
}).options({ abortEarly: false, allowUnknown: false })

export const GetWorkersListSchemaWithoutPagination = Joi.object({
    client_id: Joi.number().min(1).allow(null),
    agency_id: Joi.number().min(1).allow(null),
    site_id: Joi.number().min(1).allow(null),
    type: Joi.string().valid('TEMPORARY', 'PERMANENT')
}).options({ abortEarly: false, allowUnknown: true })

export const UpdateUserProfileSchema = Joi.object({
    name: Joi.string().allow(null, ''),
    profile: Joi.string().allow(null, ''),
})

export const AddClientUserSchema = Joi.object({
    client_role: Joi.number().min(1).required(),
    id: Joi.number().min(1).allow(null, ''),
    client_id: Joi.number().min(1).required(),
    name: Joi.string().required(),
    email: Joi.string().email().lowercase().required(),
    phone: Joi.number().min(1).max(9999999999999).allow(null, ''),
    country_code: Joi.string().pattern(/\+[0-9]+/).error(new Error('Please enter a valid country code.')).allow(null, '')
})

export const updateClientParamsSchema = Joi.object({
    id: Joi.number().min(1).required()
})

export const UpdateClientUserSchema = Joi.object({
    name: Joi.string().allow(null, ''),
    phone: Joi.number().min(1).max(9999999999999).allow(null, ''),
    country_code: Joi.string().pattern(/\+[0-9]+/).error(new Error('Please enter a valid country code.')).allow(null, ''),
    user_type_id: Joi.number().allow(null, ''),
    id: Joi.number().allow(null, '')
})

export const AddAndEditNewShiftSchema = Joi.object({
    name: Joi.string().regex(/^[\w- ]*$/).min(1).required(),
    associations: Joi.array().items(
        Joi.object({
            site_id: Joi.number().required(),
            region_id: Joi.number(),
            region_name: Joi.string(),
            site_name: Joi.string(),
        })
    ).required(),
})

const heads = Joi.object().keys({
    1: Joi.number().min(0).required().allow(""),
    2: Joi.number().min(0).required().allow(""),
    3: Joi.number().min(0).required().allow(""),
    4: Joi.number().min(0).required().allow(""),
    5: Joi.number().min(0).required().allow(""),
    6: Joi.number().min(0).required().allow(""),
    7: Joi.number().min(0).required().allow(""),
}).required();

export const createBookingSchema = Joi.object({
    repeat: Joi.number().min(0).max(12).required(),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    department_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1).required(),
    shift_type_id: Joi.number().min(1).required(),
    required_workers_heads: heads,
    required_supervisors_heads: heads,
    required_workers_total: Joi.number().min(0).required(),
    required_supervisors_total: Joi.number().min(0).required(),
    requested_total: Joi.number().min(1).required(),
    agency_requested: Joi.array().items(Joi.object({
        agency_id: Joi.number().min(1).required(),
        requested_workers_heads: heads,
        requested_supervisors_heads: heads,
        requested_workers_total: Joi.number().min(0).required(),
        requested_supervisors_total: Joi.number().min(0).required(),
        total: Joi.number().min(1).required(),
    })).required()

})

export const createBulkBookingCSVSchema = Joi.object({
    start_date: Joi.date().format(dateTimeFormates.DDMMYYYY).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.DDMMYYYY).raw().required(),
    department: Joi.string().required(),
    shift: Joi.string().regex(/^[\w- ]*$/).required(),
    agency_id: Joi.number().min(1).required(),
    agency_name: Joi.string().required(),
    total_heads: Joi.number().min(1).required(),
    supervisor_flag: Joi.string().lowercase().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO'),
    repeat: Joi.number().min(0).max(12).required(),
    sun: Joi.number().min(0).required().allow(""),
    mon: Joi.number().min(0).required().allow(""),
    tue: Joi.number().min(0).required().allow(""),
    wed: Joi.number().min(0).required().allow(""),
    thu: Joi.number().min(0).required().allow(""),
    fri: Joi.number().min(0).required().allow(""),
    sat: Joi.number().min(0).required().allow(""),
}).options({ abortEarly: false, allowUnknown: false }).error(csvValidationErrorHandler);

export const getBookingSchema = Joi.object({
    client_id: Joi.number().min(1).allow(null),
    site_id: Joi.number().min(1).allow(null),
    agency_id: Joi.number().min(1).allow(null),
    region_id: Joi.number().min(1).allow(null),
    page: Joi.number().min(1),
    limit: Joi.number().min(1),
    sort_by: Joi.string().valid(...bookingListingAllowedFields).allow(null),
    sort_type: Joi.string().valid("ASC", "DESC").allow(null, ''),
})

export const GetAdminsSchema = Joi.object({
    user_type: Joi.number().min(1).required(),
    id: Joi.number().min(1).required()
})

export const updateBookingSchema = Joi.object({
    id: Joi.number().min(1).required()
})

export const bulkUploadWorkerCsvSchema = Joi.object({
    payroll_ref: Joi.string().regex(/^[a-zA-Z0-9/-]+$/).allow(null, '').required(),
    employee_id: Joi.string().regex(/^[a-zA-Z0-9/-]+$/).min(1).required(),
    site: Joi.string().min(1).allow('').required(), //
    national_insurance_number: Joi.string().min(1).regex(/^[\w- ]*$/).allow(null, '').required(),
    first_name: Joi.string().min(1).regex(/^[\w- ]*$/).allow(null, '').required(),
    last_name: Joi.string().min(1).regex(/^[\w- ]*$/).allow(null, '').required(),
    dob: Joi.date().max('now').format(dateTimeFormates.DDMMYYYY).raw().error((errors) => {
        errors.forEach((err) => {
            if (err.code === SchemaValidationErrorCode.DATE_MAX_TODAY) {
                err.message = `Invalid '${err.local.key}' value at row number: '${err.path[0] + 1}'. \n\n Date of birth should be on or before today's date.'`
            }
        });
        return errors;
    }).allow('').required(), //
    nationality: Joi.string().min(1).regex(/^[\w,() ]*$/).custom((value, helpers) => {
        const processedValue = removeSpecialChars(value);
        if (!processedValue) {
            return helpers.error(SchemaValidationErrorCode.INVALID);
        }
        return processedValue;
    }).allow('').required(),
    sex: Joi.string().lowercase().custom((value, helpers) => {
        const newValue = sexValidation(value);
        if (!newValue) return helpers.error(SchemaValidationErrorCode.INVALID);
        else return newValue;
    }).error((errors) => {
        errors.forEach((err) => {
            if (err.code === SchemaValidationErrorCode.INVALID) {
                err.message = `Invalid Sex field Value: [ ${err.local.value} ]. \n\n It must be one of ['m', 'male', 'Male', 'f', 'female', 'Female', 'o', 'other', 'Other']`;
            }
        });
        return errors;
    }).allow("", null).required(),
    email: Joi.string().email({ minDomainSegments: 2 }).lowercase().min(1).allow(null, '').required(),//
    job_name: Joi.string().regex(/^[\w- ]*$/).allow(null, '').required(),//
    department_name: Joi.string().allow(null, '').required(),//
    shift_name: Joi.string().regex(/^[\w- ]*$/).allow('').required(),//
    role_type: Joi.string().regex(/^[\w- ]*$/).allow('').required(),//
    start_date: Joi.date().format(dateTimeFormates.DDMMYYYY).raw().allow('').required(),//
    assignment_date: Joi.date().format(dateTimeFormates.DDMMYYYY).raw().allow('').required(),//
    is_active: Joi.string().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow('').required(),
    inactivated_date: Joi.date().format(dateTimeFormates.DDMMYYYY).raw().allow('').required(),
    post_code: Joi.string().regex(/^[A-Za-z0-9 ]*$/).uppercase().min(3).allow(null, '').required(),
    limited_hours: Joi.string().default("No").valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow(null, '').required(),
    student_visa: Joi.string().default("No").valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow(null, '').required(),
    house_number: Joi.string().min(1).regex(/^[\w- ]*$/).allow(null, '').required(),
}).custom((obj, helpers) => {
    // Check if student_visa is 'Yes' (case insensitive) but limited_hours is not 'Yes'
    const studentVisaYes = ['yes', 'Yes', 'YES'].includes(obj.student_visa);
    const limitedHoursYes = ['yes', 'Yes', 'YES'].includes(obj.limited_hours);

    if (studentVisaYes && !limitedHoursYes) {
        return helpers.error(SchemaValidationErrorCode.STUDENT_VISA_LIMITED_HOURS);
    }
    return obj;
}).options({ abortEarly: false, allowUnknown: false }).error(csvValidationErrorHandler);


export const bulkUploadWorkerSchemaWithoutPagination = Joi.object({
    client_id: Joi.number().min(1).allow(null),
    agency_id: Joi.number().min(1).allow(null),
    site_id: Joi.number().min(1).allow(null),
    type: Joi.string().valid('TEMPORARY', 'PERMANENT').required()
}).options({ abortEarly: false, allowUnknown: false })

export const bulkUpdateWorkerSchemaWithoutPagination = Joi.object({
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1).allow(null),
    site_id: Joi.number().min(1).allow(null),
    type: Joi.string().valid('TEMPORARY', 'PERMANENT').required()
}).options({ abortEarly: false, allowUnknown: false })

export const timeAndAttendanceCsvSchema = Joi.object({
    payroll_ref: Joi.string().regex(/^[a-zA-Z0-9/-]+$/).min(1).allow(null, ''),
    employee_id: Joi.string().regex(/^[a-zA-Z0-9/-]+$/).min(1).required(),
    // national_insurance_number: Joi.string().allow(null, ''),
    first_name: Joi.string().min(1).required().allow(''),
    last_name: Joi.string().min(1).required().allow(''),
    client: Joi.string().min(1).required().allow(''),
    shift: Joi.string().regex(/^[\w- ]*$/).min(1).required().allow(''),
    department: Joi.string().min(1).required().allow(''),
    sun: Joi.number().default(0).required().allow(''),
    mon: Joi.number().default(0).required().allow(''),
    tue: Joi.number().default(0).required().allow(''),
    wed: Joi.number().default(0).required().allow(''),
    thu: Joi.number().default(0).required().allow(''),
    fri: Joi.number().default(0).required().allow(''),
    sat: Joi.number().default(0).required().allow(''),
    week_hours: Joi.number().required(),
    pay_rate: Joi.number().required(),
    pay_type: Joi.string().required().valid(
        PayTypes.STANDARD,
        PayTypes.OVERTIME,
        PayTypes.HOLIDAY,
        PayTypes.WEEKEND,
        PayTypes.BH,
        PayTypes.SUSPENSION,
        PayTypes.INTERNAL_STANDARD,
        PayTypes.SUPERVISOR_STANDARD,
        PayTypes.NSP,
        PayTypes.INDUCTION_TRAINING,
        PayTypes.TRAINING,
        PayTypes.SP,
        PayTypes.STANDARD_BONUS,
        PayTypes.SPECIAL_BONUS,
        PayTypes.INTERNAL_OVERTIME,
        PayTypes.INTERNAL_PERMANENT,
        PayTypes.SUPERVISOR_OVERTIME,
        PayTypes.SUPERVISOR_PERMANENT,
        PayTypes.EXPENSES
    ).insensitive(),
    standard_charges: Joi.number(),
    overtime_charges: Joi.number(),
    total_charges: Joi.number().required(),
    charge_rate: Joi.number().required(),
    standard_pay: Joi.number(),
    overtime_pay: Joi.number(),
    adjustment: Joi.string().default('No').valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow(null, '').required(),
    pay_correction: Joi.string().default('No').valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow(null, '').required()
}).options({ abortEarly: false, allowUnknown: false }).error(csvValidationErrorHandler);

export const payrollReportCsvSchema = Joi.object({
    employee_id: Joi.string().regex(/^[a-zA-Z0-9/-]+$/).min(1).required(),
    total_hour: Joi.number().required(),
    total_charge: Joi.number().required(),
    total_pay: Joi.number().required(),
    ni: Joi.number().required().allow(0),
    holiday: Joi.number().required().allow(0),
    app_levy: Joi.number().required().allow(0),
    discount: Joi.number().required().allow(0),
    pension: Joi.number().required().allow(0)
}).options({ abortEarly: false, allowUnknown: true }).error(csvValidationErrorHandler);

export const dashboardApiSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1),
    site_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    shift_id: Joi.number().min(1),
    department_id: Joi.number().min(1)
}).options({ abortEarly: false, allowUnknown: false });

export const dashboardApiWithDateFiltersSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1),
    site_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    shift_id: Joi.number().min(1),
    department_id: Joi.number().min(1),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw()
}).options({ abortEarly: false, allowUnknown: false });


export const dashboardApiWithMandatoryDateFiltersSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1),
    site_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    shift_id: Joi.number().min(1),
    department_id: Joi.number().min(1),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required()
}).options({ abortEarly: false, allowUnknown: false });

export const updateBookingForSiteAdminSchema = Joi.object({
    booking_total: Joi.number().min(1).required(),
    booking_details: Joi.array().min(1).items(Joi.object({
        booking_association_id: Joi.number().min(1).required(),
        requested_workers_heads: heads,
        requested_supervisors_heads: heads,
        requested_workers_total: Joi.number().min(0).required(),
        requested_supervisors_total: Joi.number().min(0).required(),
        requested_total: Joi.number().min(1).required(),
    })).required(),
}).options({ abortEarly: false, allowUnknown: true })


export const RevokeUserProfileAccessSchema = Joi.object({
    id: Joi.number().min(1).required()
}).options({ abortEarly: false, allowUnknown: true })

export const GetWorkerDetailsByWorkerIdSchema = Joi.object({
    id: Joi.number().min(1).required()
})

export const dashboardWithoutAgencyIdApiSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    shift_id: Joi.number().min(1),
    department_id: Joi.number().min(1),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw()
}).options({ abortEarly: false, allowUnknown: false });

export const dashboardApiWithOutAgencyIdSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    shift_id: Joi.number().min(1),
    department_id: Joi.number().min(1)
}).options({ abortEarly: false, allowUnknown: false });

export const WorkerPasswordResetRequestSchema = Joi.object({}).options({ abortEarly: false, allowUnknown: false });

export const workerRegistrationSchema = Joi.object({
    email: Joi.string().email().lowercase().required(),
    national_insurance_number: Joi.string().allow(null, ''),
    first_name: Joi.string().allow(null, ''),
    password: Joi.string().min(8).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const WorkerLoginSchema = Joi.object({
    email: Joi.string().email().lowercase().required(),
    password: Joi.string().min(8).error(new Error('Invalid email or password.')).required(),
    device_token: Joi.string()
}).options({ abortEarly: false, allowUnknown: false });


export const demographicsDashboardSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1),
    site_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    is_active: Joi.string().valid("true", "false"),
    type: Joi.string().valid("TEMPORARY", "PERMANENT")
}).options({ abortEarly: false, allowUnknown: false });

export const SendMessageRequestSchema = Joi.object({
    name: Joi.string().min(1).required(),
    title: Joi.string().min(1).required(),
    title_translations: languagesCode.allow(null).default(null),
    type: Joi.string().min(1).required().valid(
        MessageType.GENERAL,
        MessageType.AWARD,
        MessageType.BADGE,
        MessageType.RECOGNITION,
        MessageType.REWARD,
        MessageType.TRAINING
    ),
    from: Joi.string().min(1).required(),
    body: Joi.array().items(
        Joi.array().items(
            Joi.object({
                type: Joi.string().min(1).required().valid(
                    MessageBodyContentType.LINK,
                    MessageBodyContentType.MEDIA,
                    MessageBodyContentType.TEXT,
                    MessageBodyContentType.TRANSLATE,
                ),
                data: Joi.when('type', {
                    is: MessageBodyContentType.TRANSLATE,
                    then: languagesCode.required(),
                    otherwise: Joi.string().min(1).required(),
                }),
            }).required()
        ).required()
    ).required(),
    send_to: Joi.array().items(
        Joi.object({
            type: Joi.string().min(1).required().valid(
                MessageReceiverGroups.DEPARTMENT,
                MessageReceiverGroups.SHIFT,
                MessageReceiverGroups.JOB,
                MessageReceiverGroups.WORKERS,
                MessageReceiverGroups.NATIONALITY
            ),
            data: Joi.array().items().required()
        })
    ),
    is_comment_allowed: Joi.boolean().default(true).required()
}).options({ abortEarly: false, allowUnknown: false });


export const SendMessageRequestParamsSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1),
    agency_id: Joi.number().min(1),
    type: Joi.string().valid('TEMPORARY', 'PERMANENT')
}).options({ abortEarly: false, allowUnknown: false });


export const eitherClientOrAgencyId = Joi.alternatives().try(
    Joi.object({
        client_id: Joi.number().min(1).required()
    }),
    Joi.object({
        agency_id: Joi.number().min(1).required()
    })
).required().options({ abortEarly: false, allowUnknown: false }).error((errors) => {
    errors.forEach((err) => {
        if (err.code === SchemaValidationErrorCode.ALTERNATIVES) {
            err.message = 'Please provide either "client_id" or "agency_id" but not both. At least one of them is required, but not both.';
        }
    });
    return errors;
});


export const updateAutomatedMessageTranslationSchema = Joi.object({
    data: languagesCode.required()
}).options({ abortEarly: false, allowUnknown: false });

export const editAutomatedMessageSchema = Joi.object({
    from: Joi.string().min(1).required(),
    body: Joi.array().items(
        Joi.array().items(
            Joi.object({
                type: Joi.string().min(1).required().valid(
                    MessageBodyContentType.LINK,
                    MessageBodyContentType.MEDIA,
                    MessageBodyContentType.TEXT
                ),
                data: Joi.string().min(1),
            })
        ).required()
    ).required()
}).options({ abortEarly: false, allowUnknown: false });


export const workerProfileSchema = Joi.object({
    id: Joi.number().min(1).required()
})

export const UpdateWorkerProfileSchema = Joi.object({
    resource: Joi.string().uri().allow(null, ''),
    documents: Joi.object().keys({
        passport: Joi.string().uri().allow(null, ''),
        driving_license: Joi.string().uri().allow(null, ''),
        identity_card: Joi.string().uri().allow(null, ''),
        utility_bill: Joi.string().uri().allow(null, '')
    }).allow(null, '')
}).rename('profile', 'resource');


export const GetSentMessageListSchema = Joi.object({
    from: Joi.string(),
    name: Joi.string(),
    type: Joi.string().min(1).valid(
        MessageType.GENERAL,
        MessageType.AWARD,
        MessageType.BADGE,
        MessageType.RECOGNITION,
        MessageType.REWARD,
        MessageType.TRAINING,
        MessageType.SYSTEM_DEFAULT
    ),
    sort_by: Joi.string().valid().default("created_at").valid(...messageListAllowedSortingFields),
    sort_type: Joi.string().valid().default("DESC").valid(
        "ASC", "DESC"
    ),
    page: Joi.number().required(),
    limit: Joi.number().required(),
    client_id: Joi.number().min(1).allow(null, ''),
    agency_id: Joi.number().min(1).allow(null, ''),
    site_id: Joi.number().min(1).allow(null, '')
}).options({ abortEarly: false, allowUnknown: false });


export const GeTemnplateListSchema = Joi.object({
    from: Joi.string(),
    name: Joi.string(),
    type: Joi.string().min(1).valid(
        MessageType.GENERAL,
        MessageType.AWARD,
        MessageType.BADGE,
        MessageType.RECOGNITION,
        MessageType.REWARD,
        MessageType.TRAINING
    )
}).options({ abortEarly: false, allowUnknown: false });


export const getWorkerSideMessagesListSchema = Joi.object({
    type: Joi.string().min(1).valid(
        WorkerSideMessagesType.GENERAL,
        WorkerSideMessagesType.AWARD,
        WorkerSideMessagesType.BADGE,
        WorkerSideMessagesType.RECOGNITION,
        WorkerSideMessagesType.FEED,
        WorkerSideMessagesType.TRAINING,
        WorkerSideMessagesType.KUDOS
    ).required(),
    view: Joi.string().min(1).valid(
        WorkerSideMessagesScreenViewType.GENERAL,
        WorkerSideMessagesScreenViewType.AGENCY,
        WorkerSideMessagesScreenViewType.CLIENT
    ),
    page: Joi.number().required(),
    limit: Joi.number().required(),
    language_code: languageCodes,
}).options({ abortEarly: false, allowUnknown: false });


export const CreateMessageTemplateSchema = Joi.object({
    name: Joi.string().min(1).required(),
    title: Joi.string().min(1).required(),
    title_translations: languagesCode.allow(null).default(null),
    type: Joi.string().min(1).required().valid(
        MessageType.RECOGNITION,
        MessageType.AWARD,
        MessageType.BADGE,
        MessageType.GENERAL,
        MessageType.REWARD,
        MessageType.TRAINING
    ),
    from: Joi.string().min(1).required(),
    body: Joi.array().items(
        Joi.array().items(
            Joi.object({
                type: Joi.string().min(1).required().valid(
                    MessageBodyContentType.LINK,
                    MessageBodyContentType.MEDIA,
                    MessageBodyContentType.TEXT
                ),
                data: Joi.string().min(1).required()
            }).required()
        ).required()
    ).required(),
    languages: Joi.object({
        label: Joi.string().min(1).required(),
        value: Joi.string().min(1).required(),
    }).required()
}).options({ abortEarly: false, allowUnknown: false });

export const UpdateMessageTemplateSchema = Joi.object({
    name: Joi.string().min(1),
    title: Joi.string().min(1),
    title_translations: languagesCode.allow(null).default(null),
    type: Joi.string().min(1).valid(
        MessageType.RECOGNITION,
        MessageType.AWARD,
        MessageType.BADGE,
        MessageType.GENERAL,
        MessageType.REWARD,
        MessageType.TRAINING
    ),
    from: Joi.string().min(1),
    body: Joi.array().items(
        Joi.array().items(
            Joi.object({
                type: Joi.string().min(1).required().valid(
                    MessageBodyContentType.LINK,
                    MessageBodyContentType.MEDIA,
                    MessageBodyContentType.TEXT,
                    MessageBodyContentType.TRANSLATE,
                ),
                data: Joi.when('type', {
                    is: MessageBodyContentType.TRANSLATE,
                    then: languagesCode.required(),
                    otherwise: Joi.string().min(1).required(),
                }),
            }).required()
        ).required()
    ).required(),
    languages: Joi.object({
        label: Joi.string().min(1).required(),
        value: Joi.string().min(1).required(),
    })
}).options({ abortEarly: false, allowUnknown: false });

export const TrackWorkerTrainingSchema = Joi.object({
    is_training_completed: Joi.boolean().allow(null, ''),
    require_more_training: Joi.boolean().allow(null, '')
}).options({ abortEarly: false, allowUnknown: false });

export const clientRatingsSchema = Joi.object({
    client_id: Joi.number().min(1).allow(null, ''),
    agency_id: Joi.number().min(1).allow(null, '')
}).options({ abortEarly: false, allowUnknown: false });

export const siteRatingsSchema = Joi.object({
    client_id: Joi.number().min(1).allow(null, ''),
    agency_id: Joi.number().min(1).allow(null, ''),
    region_id: Joi.number().min(1).allow(null, ''),
    site_id: Joi.number().min(1).allow(null, ''),
    shift_id: Joi.number().min(1).allow(null, ''),
    department_id: Joi.number().min(1).allow(null, '')
})

export const getAllSiteSchema = Joi.object({
    client_id: Joi.number().min(1).allow(null, ''),
    region_id: Joi.number().min(1).allow(null, '')
})

export const trendsAnalysisSchema = Joi.object({
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1).allow(null, ''),
    site_id: Joi.number().min(1).allow(null, ''),
    region_id: Joi.number().min(1).allow(null, ''),
    type: Joi.string().valid("TEMPORARY", "PERMANENT")
})

export const detailedSiteRatingsSchema = Joi.object({
    client_id: Joi.number().min(1).allow(null, ''),
    region_id: Joi.number().min(1).allow(null, ''),
    type: Joi.string().valid('TEMPORARY', 'PERMANENT')
})


export const agencyRatingsSchema = Joi.object({
    client_id: Joi.number().min(1).allow(null, ''),
    agency_id: Joi.number().min(1).allow(null, '')
})

export const detailedAgencyRatingsSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
})

export const dashboardTrendsFilterSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1),
    site_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    type: Joi.string().valid("TEMPORARY", "PERMANENT")
}).options({ abortEarly: false, allowUnknown: false });

export const faqPaginationSchema = Joi.object({
    page: Joi.number().min(1).default(1),
    limit: Joi.number().min(1).default(50)
})

export const faqParamSchema = Joi.object({
    type: Joi.string().valid(
        FaqUrlType.FAQ,
        FaqUrlType.LINK_TO_SUPPORT
    ).required()
}).options({ abortEarly: false, allowUnknown: false });

export const GetMessageRequestParamsSchema = Joi.object({
    message_receiver_worker_id: Joi.number().min(1),
    language_code: languageCodes.default(Languages.ENGLISH)
});

export const GetTrainingMessageDetailesRequestParamsSchema = Joi.object({
    message_receiver_worker_id: Joi.number().min(1),
    language_code: languageCodes.default(Languages.ENGLISH)
});

export const updateBookingByAgencySchema = Joi.object({
    fulfilled_workers_heads: heads,
    fulfilled_supervisors_heads: heads,
    fulfilled_workers_total: Joi.number().min(0).required(),
    fulfilled_supervisors_total: Joi.number().min(0).required(),
    total: Joi.number().min(0).required()
})


export const PayrollListPaginationSchema = Joi.object({
    page: Joi.number().required(),
    limit: Joi.number().required(),
    sort_by: Joi.string().valid(...payrollDataAllowedSortingFields).required(),
    sort_type: Joi.string().valid("ASC", "DESC").required()
})

export const UpdateSurveyQuestionSchema = Joi.object({
    questions: Joi.array().items(Joi.object({
        id: Joi.number().min(1).required(),
        question_text: Joi.string().required()
    }))
})

export const bulkUploadJobCsvSchema = Joi.object({
    job_name: Joi.string().required(),
    department_name: Joi.string().required(),
    site_name: Joi.string().required(),
    shift_name: Joi.string().regex(/^[\w- ]*$/).required(),
    role_type: Joi.string().required(),
    hours_per_week: Joi.number().required(),
}).options({ abortEarly: false, allowUnknown: false }).error(csvValidationErrorHandler);

export const setUserProfileStatusSchema = Joi.object({
    is_active: Joi.boolean().default(true).required()
}).options({ abortEarly: false, allowUnknown: true })

export const workerRegistrationSchemaV2 = Joi.object({
    first_name: Joi.string().required(),
    surname: Joi.string().required(),
    email: Joi.string().email().lowercase().required(),
    national_insurance_number: Joi.string().allow(null, ''),
    password: Joi.string().min(8).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const updateWorkerHoursSchema = Joi.object({
    availability: Joi.string().valid('FULL TIME', 'PART TIME').required(),
    hours: Joi.number().min(0).max(37.5).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const updateWorkerLanguageCodeSchema = Joi.object({
    language: languageCodes.required()
}).options({ abortEarly: false, allowUnknown: false });

export const RestrictAgencyAssociationRequestSchema = Joi.object({
    is_restricted: Joi.boolean().required()
}).options({ abortEarly: false, allowUnknown: true })

export const RestrictCommentsForAgencyAssociationSchema = Joi.object({
    comment_restricted: Joi.boolean().required()
}).options({ abortEarly: false, allowUnknown: true })

export const addMessageReactionSchema = Joi.object({
    worker_id: Joi.number().min(1).required(),
    reaction: Joi.string().valid('LIKE', 'DISLIKE').allow(null)
}).options({ abortEarly: false, allowUnknown: false });

export const SocialFeedPathParamSchema = Joi.object({
    userId: Joi.number().min(1).required(),
    messageId: Joi.number().min(1).required()
});

export const addMessageCommentSchema = Joi.object({
    worker_id: Joi.number().min(1).required(),
    comment: Joi.string().min(1).required()
}).options({ abortEarly: false, allowUnknown: false });

export const getMessageCommentsSchema = Joi.object({
    page: Joi.number().min(1).default(1),
    limit: Joi.number().min(1).default(50),
}).options({ abortEarly: false, allowUnknown: false });

export const bulkUpdateWorkerCsvSchema = Joi.object({
    payroll_ref: Joi.string().regex(/^[a-zA-Z0-9/-]+$/).allow(null, '').required(),
    employee_id: Joi.string().regex(/^[a-zA-Z0-9/-]+$/).min(1).required(),
    site: Joi.string().min(1).allow('').required(),
    first_name: Joi.string().min(1).regex(/^[\w- ]*$/).allow('').required(),
    last_name: Joi.string().min(1).regex(/^[\w- ]*$/).allow('').required(),
    national_insurance_number: Joi.string().min(1).regex(/^[\w- ]*$/).allow('').required(),
    dob: Joi.date().max('now').format(dateTimeFormates.DDMMYYYY).raw().error((errors) => {
        errors.forEach((err) => {
            if (err.code === SchemaValidationErrorCode.DATE_MAX_TODAY) {
                err.message = `Invalid '${err.local.key}' value at row number: '${err.path[0] + 1}'. \n\n Date of birth should be on or before today's date.'`
            }
        });
        return errors;
    }).allow('').required(),
    nationality: Joi.string().min(1).regex(/^[\w,() ]*$/).custom((value, helpers) => {
        const processedValue = removeSpecialChars(value);
        if (!processedValue) {
            return helpers.error(SchemaValidationErrorCode.INVALID);
        }
        return processedValue;
    }).allow('').required(),
    sex: Joi.string().lowercase().custom((value, helpers) => {
        const newValue = sexValidation(value);
        if (!newValue) return helpers.error(SchemaValidationErrorCode.INVALID);
        else return newValue;
    }).error((errors) => {
        errors.forEach((err) => {
            if (err.code === SchemaValidationErrorCode.INVALID) {
                err.message = `Invalid Sex field Value: [ ${err.local.value} ]. \n\n It must be one of ['m', 'male', 'Male', 'f', 'female', 'Female', 'o', 'other', 'Other']`;
            }
        });
        return errors;
    }).allow("", null).required(),
    email: Joi.string().email({ minDomainSegments: 2 }).lowercase().min(1).allow('').required(),
    job_name: Joi.string().regex(/^[\w- ]*$/).allow('').required(),
    department_name: Joi.string().allow('').required(),
    shift_name: Joi.string().regex(/^[\w- ]*$/).allow('').required(),
    role_type: Joi.string().regex(/^[\w- ]*$/).allow('').required(),
    start_date: Joi.date().format(dateTimeFormates.DDMMYYYY).raw().allow('').required(),
    assignment_date: Joi.date().format(dateTimeFormates.DDMMYYYY).raw().allow('').required(),
    is_active: Joi.string().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow('').required(),
    post_code: Joi.string().regex(/^[A-Za-z0-9 ]*$/).uppercase().min(3).allow('').required(),
    student_visa: Joi.string().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow('').required(),
    house_number: Joi.string().min(1).regex(/^[\w- ]*$/).allow('').required(),
    inactivated_date: Joi.date().format(dateTimeFormates.DDMMYYYY).raw().allow('').required(),
}).options({ abortEarly: false, allowUnknown: false }).error(csvValidationErrorHandler);


export const AgencyClientAssociationSchema = Joi.object({
    client_id: Joi.number().min(1).allow(null, ''),
    agency_id: Joi.number().min(1).allow(null, ''),
    page: Joi.number().required(),
    limit: Joi.number().required(),
    sort_by: Joi.string().valid(...agencyClientAssociationTableAllowedSortingFields).required(),
    sort_type: Joi.string().valid("ASC", "DESC").required()
});

export const clientIdSchema = Joi.object({
    clientId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const agencyIdSchema = Joi.object({
    agencyId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const userIdSchema = Joi.object({
    userId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const workerIdSchema = Joi.object({
    workerId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const messageIdSchema = Joi.object({
    messageId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const regionIdSchema = Joi.object({
    regionId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const departmentIdSchema = Joi.object({
    departmentId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const sectorIdSchema = Joi.object({
    sectorId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const siteIdSchema = Joi.object({
    siteId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const shiftIdSchema = Joi.object({
    shiftId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const surveyIdSchema = Joi.object({
    surveyId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const jobIdSchema = Joi.object({
    jobId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const rateCardIdSchema = Joi.object({
    rateCardId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const associationIdSchema = Joi.object({
    associationId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const bookingIdSchema = Joi.object({
    bookingId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const payrollIdSchema = Joi.object({
    payrollId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const payrollMetaIdSchema = Joi.object({
    payrollMetaId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const templateIdSchema = Joi.object({
    templateId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const tnaIdSchema = Joi.object({
    tnaId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const trainingRuleIdSchema = Joi.object({
    trainingRuleId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const trainingRuleIdClientIdSchema = Joi.object({
    clientId: Joi.number().min(1).required(),
    trainingRuleId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const startDateYearlyRuleIdSchema = Joi.object({
    startDateYearlyRuleId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const startDateYearlyRuleIdClientIdSchema = Joi.object({
    clientId: Joi.number().min(1).required(),
    startDateYearlyRuleId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const financialRuleIdSchema = Joi.object({
    financialRuleId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const financialRuleIdClientIdSchema = Joi.object({
    clientId: Joi.number().min(1).required(),
    financialRuleId: Joi.number().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const complianceCardByIdSchema = Joi.object({
    clientId: Joi.number().min(1).required(),
    complianceCardId: Joi.number().valid(
        ComplinaceCardsType.WORKER_SIXTY_PLUS_HOURS,
        ComplinaceCardsType.MULTIPLE_OCCUPANCY,
        ComplinaceCardsType.STUDENT_VISA,
        ComplinaceCardsType.CONSECUTIVE_DAYS,
        ComplinaceCardsType.UNDER_18,
        ComplinaceCardsType.BANKING,
        ComplinaceCardsType.LIMITED_HOURS
    ).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const complianceApprovalBodySchema = Joi.object({
    clientId: Joi.number().min(1).required(),
    complianceCardId: Joi.number().valid(
        ComplinaceCardsType.MULTIPLE_OCCUPANCY,
        ComplinaceCardsType.BANKING,
    ).required(),
}).options({ abortEarly: false, allowUnknown: false });

export const complianceWorkersCountSchema = Joi.object({
    agency_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    site_id: Joi.number().min(1),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw()
}).options({ abortEarly: false, allowUnknown: true })

export const complianceWorkersListSchema = Joi.object({
    agency_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    site_id: Joi.number().min(1),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw(),
    page: Joi.number().min(1).required(),
    limit: Joi.number().min(1).required(),
    sort_by: Joi.string().valid(...complianceWorkersListAllowedSortingFields).required(),
    sort_type: Joi.string().valid("ASC", "DESC").required(),
    is_export: Joi.boolean().default(false),
    all_approved: Joi.number().valid(0, 1).default(0),
}).options({ abortEarly: false, allowUnknown: true })

export const complianceWorkersApprovalSchema = Joi.object({
    worker_id: Joi.number().min(1).required(),
    is_approved: Joi.boolean().required()
}).options({ abortEarly: false, allowUnknown: true })

export const TranslateMessageSchema = Joi.object({
    message: Joi.array().items(Joi.string()).allow(null, ''),
    from: Joi.string().allow(null, ''),
    to: Joi.string().allow(null, ''),
});

// Without TIGRINYA
export const validLanguagesToTranslate = [
    Languages.ALBANIAN,
    Languages.BULGARIAN,
    Languages.CZECH,
    Languages.ENGLISH,
    Languages.ESTONIAN,
    Languages.FRENCH,
    Languages.HINDI,
    Languages.HUNGARIAN,
    Languages.ITALIAN,
    Languages.LATVIAN,
    Languages.LITHUANIAN,
    Languages.NEPALI,
    Languages.URDU,
    Languages.UKRAINIAN,
    Languages.POLISH,
    Languages.PORTUGUESE,
    Languages.ROMANIAN,
    Languages.SLOVAK,
    Languages.SLOVANIAN,
    Languages.SPANISH
];

export const TranslateTemplateToMultipleLanguageSchema = Joi.object({
    message: Joi.array().items(Joi.string().required()),
    from: Joi.string().valid(...validLanguagesToTranslate).required(),
    title: Joi.string().allow('').required(),
    to_languages: Joi.array().items(Joi.string().valid(...validLanguagesToTranslate)).min(1).required(),
});

export const TranslateMessageToMultipleLanguageSchema = Joi.object({
    message: Joi.string().allow(null, '').required(),
    from: Joi.string().valid(...validLanguagesToTranslate).required(),
    to_languages: Joi.array().items(Joi.string().valid(...validLanguagesToTranslate)).min(1).required(),
});

export const deletePayrollSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1).required(),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
}).options({ abortEarly: false, allowUnknown: true });

export const temporaryBulkUploadWorkerCsvSchema = Joi.object({
    transport: Joi.string().default("No").valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow(null, ''),
    other_assignment: Joi.string().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow('').required(),
    pension_opt_out: Joi.string().default("Yes").valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow('').required(), //
    supervisor_status: Joi.string().lowercase()
        .custom((value, helpers) => {
            const newValue = supervisorStatisValidation(value);
            if (!newValue) return helpers.error(SchemaValidationErrorCode.INVALID);
            else return newValue;
        })
        .allow(null, '')
        .required(),
    sort_code: Joi.string().length(6).pattern(/^[0-9]+$/).allow('').required(),
    account_number: Joi.string().min(8).max(15).pattern(/^[0-9]+$/).allow('').required(),
    temp_to_perm_leaver: Joi.string().allow(null, '').optional().description('Identifies if inactive worker is temp-to-perm conversion. Leave empty for normal leavers.'),
}).custom((obj, helpers) => {
    const { sort_code, account_number } = obj;
    const isSortCodeEmpty = !sort_code; // Empty if an empty string
    const isAccountNumberEmpty = !account_number;

    if (isSortCodeEmpty !== isAccountNumberEmpty) return helpers.error(SchemaValidationErrorCode.CUSTOM_BANKDETAILS);
    return obj; // Validation passed
}).options({ abortEarly: false, allowUnknown: false }).error(csvValidationErrorHandler);

export const temporaryBulkUpdateWorkerCsvSchema = Joi.object({
    transport: Joi.string().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow('').required(),
    other_assignment: Joi.string().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow('').required(),
    pension_opt_out: Joi.string().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO').allow('').required(),
    supervisor_status: Joi.string().lowercase()
        .custom((value, helpers) => {
            const newValue = supervisorStatisValidation(value);
            if (!newValue) return helpers.error(SchemaValidationErrorCode.INVALID);
            else return newValue;
        })
        .allow(null, '')
        .required(),
    temp_to_perm_leaver: Joi.string().allow(null, '').optional().description('Identifies if inactive worker is temp-to-perm conversion. Leave empty for normal leavers.'),
});

export const setTotalAssignmentPayFlagSchema = Joi.object({
    total_assignment_pay: Joi.boolean().required()
}).options({ abortEarly: false, allowUnknown: true })

export const totalAgencyPayCsvSchema = Joi.object({
    employee_id: Joi.string().regex(/^[a-zA-Z0-9/-]+$/).min(1).required(),
    tap_value: Joi.number().required(),
}).options({ abortEarly: false, allowUnknown: true }).error(csvValidationErrorHandler);

export const deleteTAPSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1).required(),
    agency_id: Joi.number().min(1).required(),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
}).options({ abortEarly: false, allowUnknown: true });

export const getOpenBookingSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1).allow(null),
    agency_id: Joi.number().min(1).allow(null),
    region_id: Joi.number().min(1).allow(null),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
})

export const bulkFulFillBookingsCsvSchema = Joi.object({
    id: Joi.number().min(1).required(),
    client_name: Joi.string().min(1).allow('').required(),
    site: Joi.string().allow('').required(),
    department: Joi.string().allow('').required(),
    region: Joi.string().allow('').required(),
    shift: Joi.string().regex(/^[\w- ]*$/).allow('').required(),
    requested: Joi.number().min(0).required(),
    start_date: Joi.date().format(dateTimeFormates.DDMMYYYY).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.DDMMYYYY).raw().required(),
    supervisor_flag: Joi.string().lowercase().valid('Yes', 'No', 'yes', 'no', 'YES', 'NO'),
    sun: Joi.number().min(0).required(),
    mon: Joi.number().min(0).required(),
    tue: Joi.number().min(0).required(),
    wed: Joi.number().min(0).required(),
    thu: Joi.number().min(0).required(),
    fri: Joi.number().min(0).required(),
    sat: Joi.number().min(0).required(),
    total_fulfillment: Joi.number().min(0).required(),
}).options({ abortEarly: false, allowUnknown: false }).error(csvValidationErrorHandler);

export const bulkFulFillBookingsCsvSchemaWithoutPagination = Joi.object({
    client_id: Joi.number().min(1).allow(null),
    site_id: Joi.number().min(1).allow(null),
    agency_id: Joi.number().min(1).allow(null),
    region_id: Joi.number().min(1).allow(null),
})

export const RestrictWorkerInviteEmailSchema = Joi.object({
    worker_invite_email: Joi.boolean().strict().valid(true, false).required()
}).options({ abortEarly: false, allowUnknown: true })


export const UpdateAgencyAssociationSuperAdminRequestSchema = Joi.object({
    holiday_activation: Joi.boolean().strict().valid(true, false),
    holiday_cost_removed: Joi.boolean().strict().valid(true, false),
})

export const workerPerformanceAgencyCsvSchema = Joi.object({
    employee_id: Joi.string().regex(/^[a-zA-Z0-9/-]+$/).min(1).required(),
    performance_number: Joi.number().min(0).max(999).required(),
}).options({ abortEarly: false, allowUnknown: false }).error(csvValidationErrorHandler);

export const workerPerformanceClientCsvSchema = Joi.object({
    employee_id: Joi.string().regex(/^[a-zA-Z0-9/-]+$/).min(1).required(),
    performance_number: Joi.number().min(0).max(999).required(),
    agency_name: Joi.string().lowercase().required(),
}).options({ abortEarly: false, allowUnknown: false }).error(csvValidationErrorHandler);

export const workerPerformanceRequestBodySchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1).required(),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    week: Joi.number().required()
}).options({ abortEarly: false, allowUnknown: false })

export const deleteworkerPerformanceSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1).required(),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
}).options({ abortEarly: false, allowUnknown: false });

export const MarginsRequestSchema = Joi.object({
    margin: Joi.number().min(0).max(9.99).required(),
    overtime_margin: Joi.number().min(0).max(9.99).allow(null, ''),
    transport_fee: Joi.number().min(0).max(9.99).allow(null, ''),
    ssp: Joi.number().min(0).max(9.99).allow(null, ''),
    induction_training_margin: Joi.number().min(0).max(9.99).allow(null, ''),
    training_margin: Joi.number().min(0).max(9.99).allow(null, ''),
    bh_margin: Joi.number().min(0).max(9.99).allow(null, ''),
    nsp_margin: Joi.number().min(0).max(9.99).allow(null, ''),
    supervisor_standard_margin: Joi.number().min(0).max(9.99).allow(null, ''),
    supervisor_overtime_margin: Joi.number().min(0).max(9.99).allow(null, ''),
    supervisor_permanent_margin: Joi.number().min(0).max(9.99).allow(null, ''),
    suspension_margin: Joi.number().min(0).max(9.99).allow(null, '')
}).options({ abortEarly: false, allowUnknown: false });

export const MarginsQuerySchema = Joi.object({
    agency_client_association_id: Joi.number().min(1).required(),
    site_id: Joi.number().min(1).required(),
    los: Joi.number().valid(0.5, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20),
});

export const MarginIdSchema = Joi.object({
    marginId: Joi.number().required()
});

export const SiteRestrictionsRequestSchema = Joi.object({
    agency_client_association_id: Joi.number().min(1).required(),
    site_ids: Joi.array().items(Joi.number().min(1)).required()
});

export const SiteRestrictionsQuerySchema = Joi.object({
    agency_client_association_id: Joi.number().min(1).required()
});

export const performanceShiftsBlocksSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    agency_id: Joi.number().min(1),
    region_id: Joi.number().min(1),
    site_id: Joi.number().min(1),
    department_id: Joi.number().min(1),
    shift_id: Joi.number().min(1)
}).options({ abortEarly: false, allowUnknown: false });

export const siteStatsSchema = Joi.object({
    client_id: Joi.number().min(1).required(),
    region_id: Joi.number().min(1),
    start_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
    end_date: Joi.date().format(dateTimeFormates.YYYYMMDD).raw().required(),
}).options({ abortEarly: false, allowUnknown: false });

export const DeleteUserMfaEnrollmentSchema = Joi.object({
    email: Joi.string().email().lowercase().min(1).required(),
}).options({ abortEarly: false, allowUnknown: false });