import { getSignedUrlForGetObject, validateRequestData, notifyBugsnag, findDuplicatesKeyValuesInListOfObjects, dynamicErrorObject } from '../utils';
const mime = require('mime-types');
const moment = require('moment');
import { dateTimeFormates, ErrorCodes } from "../common/constants";
import { RoleTypeForCSV, UserType, workerPerformanceClientCsvSchema, workerPerformanceAgencyCsvSchema, workerPerformanceRequestBodySchema, deleteworkerPerformanceSchema, MessageActions } from "../common";
import {
    AddWorkerSchema,
    ErrorResponse,
    UpdateWorkerSchema,
    bulkUploadWorkerCsvSchema,
    GetWorkersListSchema,
    GetWorkerDetailsByWorkerIdSchema,
    WorkerLoginSchema,
    workerRegistrationSchema,
    GetWorkersListSchemaWithoutPagination,
    workerProfileSchema,
    UpdateWorkerProfileSchema,
    SendMessageRequestParamsSchema,
    updateSingleWorkerSchema,
    MimeType,
    BadRequestError, GetNationalityQueryParamsSchema, workerRegistrationSchemaV2, updateWorkerHoursSchema, searchWorkersBodySchema, bulkUpdateWorkerCsvSchema, bulkUploadWorkerSchemaWithoutPagination, workerIdSchema, messageIdSchema, userIdSchema, temporaryBulkUploadWorkerCsvSchema, updateWorkerLanguageCodeSchema, temporaryBulkUpdateWorkerCsvSchema, bulkUpdateWorkerSchemaWithoutPagination
} from '../common';
import {
    addBulkWorkers,
    addNewWorkerService,
    getWorkersListService,
    updateWorkerService,
    getWorkerDetailsByWorkerIdService,
    workerLoginService,
    documentsUploadService,
    workerRegistationService,
    getWorkersListWithoutPaginationService,
    workerProfileService,
    workerProfileServiceV2,
    updateWorkerProfileService,
    getWorkerGroupListService,
    updateWorkerDetailService,
    trackWorkerTrainingService, getWorkersNationalityService, deleteWorkerAccountService, workerRegistationServiceV2, updateWorkerDetailServiceV2, searchWorkersService, updateBulkWorkers, updateWorkerLanguageCodeService, addWorkerPerformanceService, downloadWorkerPerformanceFileService, deleteWorkerPerformanceDataService,
    divideDataByEmployeeAndEmail,
    triggerFtpLookupPythonService
} from '../services';
import { config } from '../configurations';
import { clientIdSchema, QueryParamsSchemaWithIdOnly, TrackWorkerTrainingSchema } from '../common/schema';
const path = require('path');
const csvParser = require('csvtojson');
const deepClone = require('lodash.clonedeep');

/**
 * Add new worker to the system
 * @param req Request
 * @param res Response
 * @param next
 */
export const addNewWorker = async (req, res, next) => {
    try {
        // Validate request body
        let payload = await validateRequestData(AddWorkerSchema, req.body);
        if (!payload.agency_id && !payload.client_id) {
            let errorResponse = ErrorResponse.BadRequestError;
            errorResponse.message = "'agent_id' or 'client_id' should be required."
            return res.status(400).json();
        }
        let response = await addNewWorkerService(payload, req.user.user_id);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * API to upload bulk workers data from CSV
 * @param  req Request
 * @param  res Response
 * @param  next Next
 */
export const bulkUploadWorkers = async (req, res, next) => {
    try {
        await validateRequestData(bulkUploadWorkerSchemaWithoutPagination, req.query);
        if (!req.files || (!req.query.agency_id && !req.query.client_id)) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }
        let extension = path.extname(req.files.workers.name);
        if (extension !== ".csv") {
            return res.status(400).json(ErrorResponse.InvalidFileTypeError)
        }

        let csvData = await csvParser().fromString(req.files.workers.data.toString('utf8'));
        let user_id = req.user?.user_id || 1;
        let user_type_id = req.user?.user_type_id || 0;

        let payload;
        if ([UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL, UserType.MESSAGE_ADMIN].includes(parseInt(user_type_id))) {
            payload = await validateRequestData(bulkUploadWorkerCsvSchema, csvData, true);
        } else {
            payload = await validateRequestData(bulkUploadWorkerCsvSchema.concat(temporaryBulkUploadWorkerCsvSchema), csvData, true);
        }

        if (payload.length > config.MAX_BULK_WORKER_UPLOAD_LIMIT || payload.length < 1) {
            return res.status(400).json(ErrorResponse.InvalidWorkersNumbersRequest);
        }

        const duplicates = findDuplicatesKeyValuesInListOfObjects(payload);
        if (duplicates.email.size > 0) {
            const errorObj = await dynamicErrorObject(ErrorResponse.DuplicateEntriesForEmails, Array.from(duplicates.email).join(', '))
            return res.status(400).json(errorObj);
        };

        if (duplicates.employee_id.size > 0) {
            const errorObj = await dynamicErrorObject(ErrorResponse.DuplicateEntriesForEmployeeId, Array.from(duplicates.employee_id).join(', '))
            return res.status(400).json(errorObj);
        };

        if (duplicates.national_insurance_number.size > 0) {
            const errorObj = await dynamicErrorObject(ErrorResponse.DuplicateEntriesForNI, Array.from(duplicates.national_insurance_number).join(', '))
            return res.status(400).json(errorObj);
        }

        const bulk_worker_data_list = payload.map(({
            payroll_ref,
            employee_id,
            national_insurance_number,
            first_name,
            last_name,
            dob,
            nationality,
            sex,
            email,
            job_name,
            department_name,
            shift_name,
            role_type,
            start_date,
            assignment_date,
            is_active,
            inactivated_date,
            post_code,
            transport,
            other_assignment,
            pension_opt_out,
            house_number,
            limited_hours,
            student_visa,
            site,
            supervisor_status,
            sort_code,
            account_number,
            temp_to_perm_leaver
        }) => ({
            payrollRef: payroll_ref,
            employeeId: employee_id,
            nationalInsuranceNumber: national_insurance_number.trim(),
            firstName: first_name,
            lastName: last_name,
            dateOfBirth: dob ? moment(dob, "DD-MM-YYYY").format(dateTimeFormates.YYYYMMDD) : null,
            nationality: nationality,
            orientation: sex,
            email: email,
            jobName: job_name,
            departmentName: department_name,
            shiftName: shift_name,
            roleTypeName: role_type,
            roleType: RoleTypeForCSV[role_type.trim().toUpperCase().replace(/-|_|\s/g, "")], // jobType
            jobType: RoleTypeForCSV[role_type.trim().toUpperCase().replace(/-|_|\s/g, "")],
            startDate: start_date ? moment(start_date, "DD-MM-YYYY").format(dateTimeFormates.YYYYMMDD) : null,
            assignmentDate: assignment_date ? moment(assignment_date, "DD-MM-YYYY").format(dateTimeFormates.YYYYMMDD) : null,
            isActive: is_active,
            postCode: post_code,
            transport: transport,
            otherAssignment: other_assignment || "No",
            pensionOptOut: pension_opt_out,
            houseNumber: house_number,
            limitedHours: limited_hours,
            studentVisa: student_visa,
            siteName: site,
            inActivedAt: inactivated_date ? moment(inactivated_date, "DD-MM-YYYY").format(dateTimeFormates.YYYYMMDD) : null,
            workersSupervisorStatus: supervisor_status || null,
            sortCode: sort_code || null,
            accountNumber: account_number || null,
            tempToPermLeaver: temp_to_perm_leaver || null
        }));


        const result = await divideDataByEmployeeAndEmail(bulk_worker_data_list, parseInt(req.query.client_id) || null, parseInt(req.query.agency_id) || null, req.query.type);
        const errorMessages = [
            { condition: result.userWithEmailExist.length > 0, message: `\n- -\nThe following emails are already in the system, but they're not linked to workers: \n[ ${result.userWithEmailExist.join(", ")} ]\n` },
            { condition: result.emailRequired.length > 0, message: `\n- -\nEmail is required for the following employee IDs: \n[ ${result.emailRequired.map(worker => worker.employeeId).join(", ")} ]\n` },
            { condition: result.workerExistWithDifferentAssociatedClientEmpID.length > 0, message: `\n- -\nThe following employeeIds are linked to workers under a different client:  \n[ ${result.workerExistWithDifferentAssociatedClientEmpID.map(worker => `${worker.employeeId} (Client: ${worker.clientName})`).join(", ")} ]\n` },
            { condition: result.workerExistWithDifferentAssociatedClientEmailID.length > 0, message: `\n- -\nThe following emails are linked to workers under a different client:  \n[ ${result.workerExistWithDifferentAssociatedClientEmailID.map(worker => `${worker.email} (Client: ${worker.clientName})`).join(", ")} ]\n` },
            { condition: result.workerWithEmailAlreadyExistWithDifferentAgencyOrClient.length > 0, message: `\n- -\nThe following emails are linked to workers with a different agency/client: \n[ ${result.workerWithEmailAlreadyExistWithDifferentAgencyOrClient.join(", ")} ]\n` }
        ];

        const report = errorMessages.filter(error => error.condition).map(error => error.message).join('');

        if (report) {
            return res.status(400).json({
                status: 400,
                ok: false,
                message: `There are some issues with the worker data provided:\n\n${report}`
            });
        }

        // Initialize variables for responses
        let addBulkResponse = [200, { message: "" }];
        let updateBulkResponse = [200, { message: "" }];

        if (result.workersForBulkUpload.length > 0) {
            const missingFields = {
                requiredFields: [],
                dates: [],
                emails: [],
                dob: []
            };

            result.workersForBulkUpload.forEach(({ employeeId, siteName, jobName, departmentName, shiftName, roleTypeName, startDate, assignmentDate, email, dateOfBirth }) => {
                if (!siteName || !jobName || !departmentName || !shiftName || !roleTypeName) missingFields.requiredFields.push(employeeId);
                if (!startDate || !assignmentDate) missingFields.dates.push(employeeId);
                if (!email) missingFields.emails.push(employeeId);
                if (!dateOfBirth) missingFields.dob.push(employeeId);
            });

            const errorMessages = [];
            if (missingFields.requiredFields.length) errorMessages.push(`Missing site, job_name, department_name, shift_name, or role_type for employee IDs: [${missingFields.requiredFields.join(', ')}]`);
            if (missingFields.dates.length) errorMessages.push(`Missing start_date or assignment_date for employee IDs: [${missingFields.dates.join(', ')}]`);
            if (missingFields.emails.length) errorMessages.push(`Missing email for employee IDs: [${missingFields.emails.join(', ')}]`);
            if (missingFields.dob.length) errorMessages.push(`Missing dob for employee IDs: [${missingFields.dob.join(', ')}]`);

            if (errorMessages.length) {
                return res.status(400).json({
                    status: 400,
                    ok: false,
                    message: errorMessages.join('\n\n')
                });
            }
            addBulkResponse = await addBulkWorkers(
                result.workersForBulkUpload,
                parseInt(user_id),
                parseInt(req.query.client_id) || null,
                parseInt(req.query.agency_id) || null,
                parseInt(req.query.site_id) || null,
                req.query.type
            );
        }

        if (result.workersForBulkUpdate.length || result.returningWorkersForBulkUpdate.length) {
            updateBulkResponse = await updateBulkWorkers(
                result.workersForBulkUpdate,
                result.returningWorkersForBulkUpdate,
                req.query,
                parseInt(user_id)
            );
        }

        let combinedErrorMessage = '';

        if (addBulkResponse?.[0] !== 200 && addBulkResponse[0] !== 201) {
            combinedErrorMessage += `NEW WORKER UPLOADS\n${addBulkResponse[1]["message"]}\n\n`;
        }

        if (updateBulkResponse?.[0] !== 200 && updateBulkResponse[0] !== 201) {
            combinedErrorMessage += combinedErrorMessage ? `----------------------------------\n\n` : '';
            combinedErrorMessage += `WORKER UPDATES\n${updateBulkResponse[1]["message"]}\n\n`;
        }

        if (combinedErrorMessage) {
            return res.status(400).json({
                status: 400,
                ok: false,
                message: combinedErrorMessage.trim()
            });
        }

        return res.status(200).json({
            ok: true,
            message: MessageActions.UPDATE_WORKERS
        });

    } catch (err) {
        if (ErrorCodes.wrongValueForField.includes(err.code)) {
            return res.status(422).json(ErrorResponse.UnprocessableEntityForFile)
        }
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to update single Workers Activity Status
 * @param  req Request
 * @param  res Response
 * @param  next Next
 */
export const updateSingleWorkersActivityStatus = async (req, res, next) => {
    try {
        await validateRequestData(UpdateWorkerSchema, req.body);
        let response = await updateWorkerService(req.body, req.user);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * API to download sample file to upload the Bulk workers.
 * @param req
 * @param res
 * @param next
 */
export const downloadSampleFile = async (req, res, next) => {
    try {
        let link;
        if ([UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL, UserType.MESSAGE_ADMIN].includes(parseInt(req.user.user_type_id))) {
            link = await getSignedUrlForGetObject(config.BUCKET_NAME, config.WORKER_BUCKET_FOLDER, config.PERMANENT_WORKER_SAMPLE_DOWNLOAD_BUCKET_KEY);
        } else {
            link = await getSignedUrlForGetObject(config.BUCKET_NAME, config.WORKER_BUCKET_FOLDER, config.WORKER_SAMPLE_DOWNLOAD_BUCKET_KEY);
        }
        res.status(200).json({
            ok: true,
            "resource_url": link.url,
        })
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to GET workers list.
 * @param req
 * @param res
 * @param next
 */
export const getWorkersList = async (req, res, next) => {
    try {
        await validateRequestData(GetWorkersListSchema, req.query);
        let response = await getWorkersListService(req.query, req.user.user_type_id);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}


/**
 * API to search workers.
 * @param req
 * @param res
 * @param next
 */
export const searchWorkers = async (req, res, next) => {
    try {
        await validateRequestData(GetWorkersListSchema, req.query);
        let body = await validateRequestData(searchWorkersBodySchema, req.body);

        let response = await searchWorkersService(body, req.query, req.user.user_type_id);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}


/**
 * API to get the worker details by worker ID.
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const getWorkerDetailsByWorkerId = async (req, res, next) => {
    try {
        await validateRequestData(workerIdSchema, req.params);
        let response = await getWorkerDetailsByWorkerIdService(req.params.workerId, req.user);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to update the worker password by natioal insurance number and email.
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const workerRegistrationAPI = async (req, res, next) => {
    try {
        await validateRequestData(workerRegistrationSchema, req.body);
        let response = await workerRegistationService(req.body, req.user);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to GET the workers list without pagination.
 * @param req
 * @param res
 * @param next
 */
export const getWorkersListWithoutPagination = async (req, res, next) => {
    try {
        await validateRequestData(GetWorkersListSchemaWithoutPagination, req.query);
        let response = await getWorkersListWithoutPaginationService(req.query);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API for worker login.
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const workerLogin = async (req, res, next) => {
    try {
        await validateRequestData(WorkerLoginSchema, req.body);
        let response = await workerLoginService(req.body);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API for worker login.
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const workerDocumentsUpload = async (req, res, next) => {
    try {

        let mimeTypes = Object.values(MimeType);
        let files = Object.keys(JSON.parse(JSON.stringify(deepClone(req.files))));

        files.forEach(key => {
            if (!mimeTypes.includes(JSON.parse(JSON.stringify(deepClone(req.files[key].mimetype))))) {
                throw new BadRequestError("BAD_REQUEST", `Invalid file type ${mime.extension(req.files[key].mimetype)}`)
            }
        })

        let response = await documentsUploadService(req.files);
        return res.status(response[0]).json(response[1]);

    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to get the worker profile details including worker training data, shifts completed and length of service.
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const workerProfileAPI = async (req, res, next) => {
    try {

        if (req.params.userId != req.user.user_id) {
            return res.status(403).send(ErrorResponse.PermissionDenied);
        }

        await validateRequestData(userIdSchema, req.params);
        let response = await workerProfileService(req.user.user_id);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to get the worker profile details including worker training data, shifts completed and length of service.
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const workerProfileAPIV2 = async (req, res, next) => {
    try {

        if (req.params.userId != req.user.user_id) {
            return res.status(403).send(ErrorResponse.PermissionDenied);
        }

        await validateRequestData(userIdSchema, req.params);
        let response = await workerProfileServiceV2(req.user.user_id);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to update worker profile by user id
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const updateWorkerProfileByUserId = async (req, res, next) => {
    try {

        if (req.params.userId != req.user.user_id) {
            return res.status(403).send(ErrorResponse.PermissionDenied);
        }

        // Validate query params
        await validateRequestData(userIdSchema, req.params);
        // Validate request body
        let validateData = await validateRequestData(UpdateWorkerProfileSchema, req.body);
        let response = await updateWorkerProfileService(req.user.user_id, validateData);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

export const updateWorkerDetailByWorkerId = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(workerIdSchema, req.params);
        // Validate request body
        await validateRequestData(updateSingleWorkerSchema, req.body);
        let response = await updateWorkerDetailService(req.params.workerId, req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to get list of worker groups
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const getWorkerGroupDetails = async (req, res, next) => {
    try {
        await validateRequestData(SendMessageRequestParamsSchema, req.query);
        let response = await getWorkerGroupListService(req.query);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to track the worker training status.
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const trackWorkerTrainingAPI = async (req, res, next) => {
    try {
        await validateRequestData(messageIdSchema, req.params)
        await validateRequestData(TrackWorkerTrainingSchema, req.body);
        let response = await trackWorkerTrainingService(req.params.messageId, req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}


/**
 * Get list of nationalities for the workers as per provided site
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const getWorkersNationality = async (req, res, next) => {
    try {
        await validateRequestData(GetNationalityQueryParamsSchema, req.query);
        let response = await getWorkersNationalityService(req.query);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * API to delete the worker account by user id
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const deleteWorkerAccount = async (req, res, next) => {
    try {
        let response = await deleteWorkerAccountService(req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}


/**
 * API to register worker with mobile APP.
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const workerRegistrationAPIV2 = async (req, res, next) => {
    try {
        await validateRequestData(workerRegistrationSchemaV2, req.body);
        let response = await workerRegistationServiceV2(req.body);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

export const updateWorkerDetailByWorkerIdV2 = async (req, res, next) => {
    try {
        // Validate request body
        await validateRequestData(updateWorkerHoursSchema, req.body);
        let response = await updateWorkerDetailServiceV2(req.user, req.body);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

export const updateWorkerLanguageCode = async (req, res, next) => {
    try {
        // Validate request body
        await validateRequestData(updateWorkerLanguageCodeSchema, req.body);
        let response = await updateWorkerLanguageCodeService(req.user, req.body);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to update bulk workers data from CSV
 * @param  req Request
 * @param  res Response
 * @param  next Next
 */
export const bulkUpdateWorker = async (req, res, next) => {
    try {
        await validateRequestData(bulkUpdateWorkerSchemaWithoutPagination, req.query);
        if (!req.files) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }
        let extension = path.extname(req.files.workers.name);
        if (extension !== ".csv") {
            return res.status(400).json(ErrorResponse.InvalidFileTypeError)
        }

        let csvData = await csvParser().fromString(req.files.workers.data.toString('utf8'));

        let payload;
        if ([UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL, UserType.MESSAGE_ADMIN].includes(parseInt(req.user.user_type_id))) {
            payload = await validateRequestData(bulkUpdateWorkerCsvSchema, csvData, true);
        } else {
            // Temporary worker
            payload = await validateRequestData(bulkUpdateWorkerCsvSchema.concat(temporaryBulkUpdateWorkerCsvSchema), csvData, true);
        }

        if (payload.length > config.MAX_BULK_WORKER_UPLOAD_LIMIT || payload.length < 1) {
            return res.status(400).json(ErrorResponse.InvalidWorkersNumbersRequest);
        }

        const duplicates = findDuplicatesKeyValuesInListOfObjects(payload);
        if (duplicates.email.size > 0) {
            const errorObj = await dynamicErrorObject(ErrorResponse.DuplicateEntriesForEmails, Array.from(duplicates.email).join(', '))
            return res.status(400).json(errorObj);
        };

        if (duplicates.employee_id.size > 0) {
            const errorObj = await dynamicErrorObject(ErrorResponse.DuplicateEntriesForEmployeeId, Array.from(duplicates.employee_id).join(', '))
            return res.status(400).json(errorObj);
        };

        if (duplicates.national_insurance_number.size > 0) {
            const errorObj = await dynamicErrorObject(ErrorResponse.DuplicateEntriesForNI, Array.from(duplicates.national_insurance_number).join(', '))
            return res.status(400).json(errorObj);
        }

        const bulk_worker_data_list = payload.map(({
            payroll_ref,
            employee_id,
            national_insurance_number,
            first_name,
            last_name,
            dob,
            nationality,
            sex,
            job_name,
            department_name,
            shift_name,
            role_type,
            start_date,
            assignment_date,
            is_active,
            post_code,
            transport,
            other_assignment,
            pension_opt_out,
            house_number,
            student_visa,
            site,
            email,
            inactivated_date,
            supervisor_status,
            temp_to_perm_leaver
        }) => ({
            payrollRef: payroll_ref,
            employeeId: employee_id,
            nationalInsuranceNumber: national_insurance_number.trim(),
            firstName: first_name,
            lastName: last_name,
            dateOfBirth: dob ? moment(dob, "DD-MM-YYYY").format(dateTimeFormates.YYYYMMDD) : null,
            nationality: nationality,
            orientation: sex,
            jobName: job_name,
            departmentName: department_name,
            shiftName: shift_name,
            jobType: RoleTypeForCSV[role_type.trim().toUpperCase().replace(/-|_|\s/g, "")],
            startDate: start_date ? moment(start_date, "DD-MM-YYYY").format(dateTimeFormates.YYYYMMDD) : null,
            assignmentDate: assignment_date ? moment(assignment_date, "DD-MM-YYYY").format(dateTimeFormates.YYYYMMDD) : null,
            isActive: is_active,
            postCode: post_code,
            transport: transport,
            otherAssignment: other_assignment,
            pensionOptOut: pension_opt_out,
            houseNumber: house_number,
            studentVisa: student_visa,
            siteName: site,
            email: email,
            inActivedAt: inactivated_date ? moment(inactivated_date, "DD-MM-YYYY").format(dateTimeFormates.YYYYMMDD) : null,
            workersSupervisorStatus: supervisor_status || null,
            tempToPermLeaver: temp_to_perm_leaver || null
        }));

        let response = await updateBulkWorkers(bulk_worker_data_list, [], req.query, parseInt(req.user.user_id));
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to download sample file to update the Bulk workers.
 * @param req
 * @param res
 * @param next
 */
export const downloadSampleFileToUpdateWorker = async (req, res, next) => {
    try {
        let link;
        if ([UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL, UserType.MESSAGE_ADMIN].includes(parseInt(req.user.user_type_id))) {
            link = await getSignedUrlForGetObject(config.BUCKET_NAME, config.WORKER_BUCKET_FOLDER, config.WORKER_UPDATE_CLIENT_SAMPLE_FILE_NAME);
        } else {
            // Temporary worker
            link = await getSignedUrlForGetObject(config.BUCKET_NAME, config.WORKER_BUCKET_FOLDER, config.WORKER_UPDATE_AGENCY_SAMPLE_FILE_NAME);
        }

        res.status(200).json({
            ok: true,
            "resource_url": link.url,
        })
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/* API to upload Worker Performance Data */
export const uploadWorkerPerformance = async (req, res, next) => {
    try {
        if (!req.files) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }
        let extension = path.extname(req.files.workerPerformance.name);
        if (extension !== ".csv") {
            return res.status(400).json(ErrorResponse.InvalidFileTypeError)
        }
        let csv = String(req.files.workerPerformance.data)
        let csvData = await csvParser().fromString(req.files.workerPerformance.data.toString('utf8'));

        let payload;
        let isClientUser = 0;
        if ([UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL].includes(parseInt(req.user.user_type_id))) {
            payload = await validateRequestData(workerPerformanceClientCsvSchema, csvData, true);
            isClientUser = 1;
        } else {
            payload = await validateRequestData(workerPerformanceAgencyCsvSchema, csvData, true);
        }

        if (!payload.length) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }

        let body = await validateRequestData(workerPerformanceRequestBodySchema, req.query);
        body.isClientUser = isClientUser;

        const duplicates = findDuplicatesKeyValuesInListOfObjects(payload, true);
        if (duplicates.employee_id.size > 0) {
            const errorObj = await dynamicErrorObject(ErrorResponse.DuplicateEntriesForEmployeeId, Array.from(duplicates.employee_id).join(', '))
            return res.status(400).json(errorObj);
        };

        let response = await addWorkerPerformanceService(csv, payload, body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}


/**
 * API to download uploaded worker performance file
 * @param req
 * @param res
 * @param next
 */
export const downloadWorkerPerformanceFile = async (req, res, next) => {
    try {
        await validateRequestData(workerPerformanceRequestBodySchema, req.query);
        let isClientUser = 0;
        if ([UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL].includes(parseInt(req.user.user_type_id))) {
            isClientUser = 1;
        }
        req.query.isClientUser = isClientUser;

        let response = await downloadWorkerPerformanceFileService(req.user, req.query);
        res.status(response[0]).json(response[1]);
    }
    catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Delete TAP file
 * @param req Request
 * @param res Response
 * @param next
 */
export const deleteWorkerPerformanceFile = async (req, res, next) => {
    try {
        // Validate request body
        let payload = await validateRequestData(deleteworkerPerformanceSchema, req.body);

        let isClientUser = 0;
        if ([UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL].includes(parseInt(req.user.user_type_id))) {
            isClientUser = 1;
        }
        payload.isClientUser = isClientUser;

        let response = await deleteWorkerPerformanceDataService(payload, req.user);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 * API to download sample file to upload worker performance
 * @param req
 * @param res
 * @param next
 */
export const downloadWorkerPerformanceSampleSheet = async (req, res, next) => {
    try {
        let link;
        if ([UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL, UserType.MESSAGE_ADMIN].includes(parseInt(req.user.user_type_id))) {
            link = await getSignedUrlForGetObject(config.BUCKET_NAME, config.WORKER_PERFORMANCE_SAMPLE_SHEET_FOLDER, config.WORKER_PERFORMANCE_CLIENT_SAMPLE_SHEET);
        } else {
            // Temporary worker
            link = await getSignedUrlForGetObject(config.BUCKET_NAME, config.WORKER_PERFORMANCE_SAMPLE_SHEET_FOLDER, config.WORKER_PERFORMANCE_AGENCY_SAMPLE_SHEET);
        }

        res.status(200).json({
            ok: true,
            "resource_url": link.url,
        })
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}


export const triggerFtpLookupWorkersUpload = async (req, res, next) => {
    try {

        await validateRequestData(clientIdSchema, req.params);
        let response = await triggerFtpLookupPythonService(req.params.clientId, req.user, 'WORKERS_UPLOAD');
        res.status(response[0]).json(response[1]);

    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};