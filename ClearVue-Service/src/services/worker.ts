/**
 * All the service layer methods for the Workers.
 */
import {
    <PERSON>rrorCodes,
    ErrorResponse,
    GetWorkersDTO,
    UpdateWorkerDTO,
    AddWorkerDTO,
    BadRequestError,
    bcryptSaltRound,
    UserType, MessageActions, AutomatedMessagesLabels, RoleType, WorkerTypes, WorkerAvailability, message, workerInviteEmailSubjectLine,
    agency
} from "../common";
import {
    prepareAndSendAutomatedEventMessages, sendWorkAnniversaryGreetingsIfEligible, getJobListingForDropDownService,
    getAllSites
} from "../services";
const uuid = require('uuid');
const deepClone = require('lodash.clonedeep');
import {
    addNewWorker,
    addNewWorkers,
    updateWorkers,
    getRequestedUserEmailCounts,
    getWorkers,
    getWorkerDetailsHelper,
    getJobsByClientID,
    updateWorkerHelper,
    getWorkerHelper,
    getWorkersWithoutPagination,
    nationalInsuranceNumberExistsHelper,
    getUserByNationalInsuranceNumber,
    createWorkerUser,
    addWorkerUserInBulk,
    getUserIdByNationalInsuranceNumber,
    bulkUpdateUserId,
    updateUser,
    getUserById,
    getWorkerIdfromUserId,
    updateWorkerDetail,
    getWorkerByWorkerId,
    updateUserHelper, createDepartment, createJobAssociation,
    getWorkerUserDetails, getWorkerLengthOfServiceByWorkerId, getWorkerShiftsCompleted, updateWorkerProfile,
    getWorkerTrainingData, getAllWorkerGroup, getWorkerAppreciationDataFromUserIdHelper,
    getWorkerIdFromUserIdAndAgencyId, getMessageDetailsModel, trackWorkerTrainingHelper,
    getWorkerDetailsByMessageIdAndUserId, getAdminEmailsFromSiteId, getAgencyAdminEmailByAgencyId, getDetailsWorkerId,
    getWorkersByNationalInsuranceNumber, updateWorkerNationalInsuranceNumber,
    getExistingNationalInsuranceWithAgency, getExistingEmployeeIdWithClientORAgency, getNationalityOfWorkers, getCompletedTrainingCount,
    getMessageDetailsByLabel, revokeUserProfileAccessHelper, getAgencyById, getClientsById, searchWorkersList, getExistingEmployeeId, updateWorkersData,
    getWorkerAutomatedMessageInfoToRemove, deleteMessageReceiverWorkersRawsById, updateSystemMessage, getRequestedUserEmail, getWorkerIdFromMessageId, bulkUpdateUserDetails, setNullNiNumberUser, setNullNiNumberWorker,
    getSitesByNames, getAllJobsByClientID, getUserByEmailList, getExistingNationalInsuranceWithClient,
    updateTotalAgencyPayDataByEmployeeId,
    createPensionStatusLog,
    getAllUsers,
    getSiteById,
    getAgencyAssociationByAgencyIdAndClientId,
    getWorkerPerformance,
    createWorkerPerformance,
    getWorkerPerformanceById,
    deleteWorkerPerformanceById,
    deleteWorkerPerformanceDataById,
    addWorkerPerformanceData,
    getWorkerByEmployeeIdAndAgencyId,
    getAgencyAssociationByClientId,
    getWorkersByEmployeeIdAndAgencyIds,
    getTimeAndAttendance,
    getWorkerPerformanceDataHelper,
    getLastTwoPerformancesByWorkerId,
    deleteWorkerPerformanceDataHelper,
    deletePayrollData,
    fetchWorkersByEmailsAndAgency,
    createPensionStatusLogTransaction,
    updateUserTransaction,
    updateWorkersDataTransaction,
    bulkUpdateUserDetailsTransaction, bulkUpdateUserIdTransaction, setNullNiNumberUserTransaction, setNullNiNumberWorkerTransaction,
    addNewWorkersTransaction,
    addWorkerUserInBulkTransaction,
    getWorkersByCriteria,
    insertComplianceApprovalTx,
    getComplianceApprovalForWorkersIfExists
} from "../models";
const path = require('path');
let _ = require("lodash");
import { invokeApi, notifyBugsnag, sendTemplateEmail, sendTemplateEmailInBulk, uploadFileOnS3, diffBetweenGivenDateAndTodayInYear, getTodaysDate, stripEmojis, diffBetweenGivenTwoDatesInDays, deleteObject, splitArrayWithSize, getNewTransaction, getSignedUrlForGetObject, dynamicErrorObject, getDurationBetweenDates } from "../utils";
import { sendUnassignedWorkerMessages } from "."
const jwt = require("jsonwebtoken");
import { config } from "../configurations";
import moment from "moment";
import { getWorkerWhereClauseString } from './dashboard';
const bcrypt = require('bcryptjs');
import { sendAutomatedEventMessages } from './automatedMessages';
import { ComplinaceCardsType, PensionStatus, TimeAndAttendanceStatus, WeekDays } from "../common/enum";
import { response } from "express";
const { EasyPromiseAll } = require('easy-promise-all');
import { getManager } from "typeorm";


/**
 * Add new worker service
 * @param  {AddWorkerDTO} requestPayload
 * @param {string} loggedInUserId
 */
export const addNewWorkerService = async (requestPayload: AddWorkerDTO, loggedInUserId: string) => {

    try {
        const isSortCodeEmpty = !requestPayload.sort_code; // Empty if an empty string
        const isAccountNumberEmpty = !requestPayload.account_number;
        if (isSortCodeEmpty !== isAccountNumberEmpty) return [401, ErrorResponse.MissingBankDetialsError];

        if ((await validateWorkerEmailWithExistingUsers([requestPayload.email])).totalUser) {
            return [409, ErrorResponse.WorkerEmailAlreadyExists];
        }
        let userId;

        if (requestPayload.national_insurance_number === null || requestPayload.national_insurance_number === undefined || requestPayload.national_insurance_number.trim() === "") {
            requestPayload.national_insurance_number = `ni_prefix${uuid.v4()}`;
        }

        requestPayload.national_insurance_number = requestPayload.national_insurance_number.trim();

        let availableNI;
        if (requestPayload.type == WorkerTypes.PERMANENT) {
            // Checking the existing national insurance number exists with client id
            availableNI = await getExistingNationalInsuranceWithClient([requestPayload.national_insurance_number], requestPayload.client_id);
        } else {
            // Checking the existing national insurance number exists with agency id
            availableNI = await getExistingNationalInsuranceWithAgency([requestPayload.national_insurance_number], requestPayload.agency_id);
        }

        if (_.size(availableNI)) {
            const errorObj = await dynamicErrorObject(ErrorResponse.NationalInsuranceNumberExistsErrorCustom, requestPayload.national_insurance_number)
            return [422, errorObj]
        }

        if (requestPayload.start_date > requestPayload.assignment_date) {
            return [404, ErrorResponse.InvalidWorkerAssignmentDates]
        }

        // Check national insurance number exists or not
        if (await validateNationalInsuranceNumber(requestPayload.national_insurance_number)) {
            ({ id: userId } = await getUserByNationalInsuranceNumber(requestPayload.national_insurance_number))
        }

        // get the ID of the newly added user and then insert the record into the worker table.
        else {
            ({ id: userId } = await createWorkerUser(requestPayload, loggedInUserId));
        }

        let response = await addNewWorker(requestPayload, userId, loggedInUserId);

        if (requestPayload.national_insurance_number.includes('ni_prefix')) {
            await setNullNiNumberUser(JSON.stringify(requestPayload.national_insurance_number));
            await setNullNiNumberWorker(JSON.stringify(requestPayload.national_insurance_number));
        }

        // Send worker invite email
        let clientDetails = await getClientsById(requestPayload.client_id);
        if (clientDetails.worker_invite_email) {
            let message = {
                toEmailId: requestPayload.email,
                templateId: config.Sendgrid.WORKER_INVITE_EMAIL_TEMPLATE,
                dynamicTemplateData: {
                    subject_line: workerInviteEmailSubjectLine,
                    worker_name: requestPayload.first_name
                },
            };
            await sendTemplateEmail(message);
        }

        return [201, {
            'ok': true,
            message: MessageActions.CREATE_WORKER,
            'worker_id': parseInt(response.id)
        }]
    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.WorkerAlreadyExists]    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }

};


export const divideDataByEmployeeAndEmail = async (data, clientId, agencyId, type) => {
    const workersForBulkUpdate = [];
    const returningWorkersForBulkUpdate = [];
    const workersForBulkUpload = [];

    const userWithEmailExist = [];
    const emailRequired = [];
    const workerExistWithDifferentAssociatedClientEmpID = [];
    const workerExistWithDifferentAssociatedClientEmailID = [];
    const workerWithEmailAlreadyExistWithDifferentAgencyOrClient = [];

    // Extract employeeId array from data
    const employeeIdArray = data.map(worker => worker.employeeId);

    // Get workers from the Workers table based on type
    const workers = await getWorkersByCriteria(clientId, agencyId, type, employeeIdArray);
    // Create a map for quick lookup of workers by employeeId
    const workerMap = new Map();
    workers.forEach(worker => workerMap.set(worker.employeeId, worker));

    // Find workers that are in the workers table
    data.forEach(worker => {
        const matchedWorker = workerMap.get(worker.employeeId);
        if (matchedWorker) {
            worker.clientId = matchedWorker.clientId;
            worker.clientName = matchedWorker.clientName;
            worker.agencyId = matchedWorker.agencyId;
        }
    });

    const workersInSheetThatExist = data.filter(worker => workerMap.has(worker.employeeId));

    let filteredWorkerWithSameClient = [];
    if (type != WorkerTypes.PERMANENT) {
        // 1. Filter agency workers based on clientId
        filteredWorkerWithSameClient = workersInSheetThatExist.filter(worker => worker.clientId == clientId);

        // 2. Find remaining agency workers (those with a different client)
        const remainingInAgencyWorkers = workersInSheetThatExist.filter(worker => worker.clientId != clientId);
        remainingInAgencyWorkers.forEach(worker => {
            workerExistWithDifferentAssociatedClientEmpID.push({ ...worker });
        });
    }
    // Process existing workers for bulk update

    (filteredWorkerWithSameClient.length ? filteredWorkerWithSameClient : workersInSheetThatExist).forEach(worker => {
        const user = workerMap.get(worker.employeeId)

        if (worker.email && user.email == worker.email) {
            worker.email = '';
        }
        if (worker.nationalInsuranceNumber && user.nationalInsuranceNumber == worker.nationalInsuranceNumber) {
            worker.nationalInsuranceNumber = '';
        }

        delete worker.agencyId
        delete worker.clientId
        delete worker.clientName
        workersForBulkUpdate.push(worker);
    });
    // Create a list of emails from workers for batch lookup
    const workersInSheetWithEmailValueEmails = data.map(worker => worker.email).filter(email => email);

    const existingUsersInSystemWithGivenEmail = await getUserByEmailList(workersInSheetWithEmailValueEmails);
    // Find remaining workers that are not in existing workers
    const remainingWkrInSheet = data.filter(worker => !workerMap.has(worker.employeeId));
    // Check remaining workers for email requirements and bulk uploads
    for (const worker of remainingWkrInSheet) {
        if (!worker.email) {
            emailRequired.push(worker); // Add to emailRequired if email is null/empty
        } else {
            // Check if the email exists in the User table
            const user = existingUsersInSystemWithGivenEmail.find(user => user.email.toLowerCase() == worker.email);
            if (!user) {
                // New user, add to bulk upload
                workersForBulkUpload.push(worker);
            } else if (user.user_type_id != 6) {
                // Existing user but not temporary
                userWithEmailExist.push(user.email);
            } else {
                // User is a temporary worker
                if (!user.client_id && !user.agency_id) {
                    // Individual workers (no agencyId and clientId)
                    workersForBulkUpload.push(worker);
                } else if (type != WorkerTypes.PERMANENT && user.agency_id != agencyId) {
                    workerWithEmailAlreadyExistWithDifferentAgencyOrClient.push(worker.email);
                }
                else if (user.client_id != clientId) {
                    // Workers with a different clientId
                    if (type != WorkerTypes.PERMANENT) {
                        workerExistWithDifferentAssociatedClientEmailID.push({ ...worker, clientName: user.client_name });
                    } else {
                        workerWithEmailAlreadyExistWithDifferentAgencyOrClient.push(worker.email);
                    }
                } else {
                    if (worker.nationalInsuranceNumber && user.national_insurance_number == worker.nationalInsuranceNumber) {
                        worker.nationalInsuranceNumber = '';
                    }
                    // Returning worker that needs an update
                    returningWorkersForBulkUpdate.push(worker);
                }
            }
        }
    }

    // Returning the categorized results
    return {
        workersForBulkUpdate,
        returningWorkersForBulkUpdate,
        workersForBulkUpload,
        userWithEmailExist,
        emailRequired,
        workerExistWithDifferentAssociatedClientEmpID,
        workerExistWithDifferentAssociatedClientEmailID,
        workerWithEmailAlreadyExistWithDifferentAgencyOrClient,
    };
};

/**
 * Service to add bulk workers data.
 * @param  {} data
 */
export const addBulkWorkers = async (data, userId, clientId, agencyId, siteId, workerType) => {
    try {
        for (let element of data) {
            if (element.nationalInsuranceNumber === null || element.nationalInsuranceNumber === undefined || element.nationalInsuranceNumber.trim() === "") {
                element.nationalInsuranceNumber = `ni_prefix${uuid.v4()}`;
            }
        }

        // Checking the existing national insurance number exists with agency id
        let nationalInsuranceNumberArray = data.map(worker => worker.nationalInsuranceNumber);

        let availableNI;
        if (workerType == WorkerTypes.PERMANENT) {
            // Checking the existing national insurance number exists with client id for permanent worker
            availableNI = await getExistingNationalInsuranceWithClient(nationalInsuranceNumberArray, clientId);
        } else {
            // Checking the existing national insurance number exists with agency id
            availableNI = await getExistingNationalInsuranceWithAgency(nationalInsuranceNumberArray, agencyId);
        }

        if (_.size(availableNI)) {
            let availableNIList = _.map(availableNI, 'nationalInsuranceNumber');
            const errorObj = await dynamicErrorObject(ErrorResponse.NationalInsuranceNumberExistsErrorCustom, availableNIList)
            return [422, errorObj]
        }

        // Checking the existing employee id exists with agency id
        let employeeIdArray = data.map(worker => worker.employeeId); // if temp then check with agency if perm then check with client id.
        let availableEmpId = await getExistingEmployeeIdWithClientORAgency(employeeIdArray, agencyId, workerType);
        if (_.size(availableEmpId)) {
            let availableEmpIdList = _.map(availableEmpId, 'employeeId');
            const errorObj = await dynamicErrorObject(ErrorResponse.EmployeeIdExistsCustom, availableEmpIdList)
            return [422, errorObj]
        }

        // Handle bulk worker inactivate scenarios
        let FoundInactivatedDateForActiveWorker = data.filter(employee => employee.isActive.toString().toLowerCase() === "yes" && employee.inActivedAt && employee.inActivedAt !== '').map(employee => employee.employeeId);
        let FoundInactivatedDateForBlankActiveFlag = data.filter(employee => (employee.isActive === null || employee.isActive === '') && employee.inActivedAt && employee.inActivedAt !== '').map(employee => employee.employeeId);
        let InactivatedDateNotFound = data.filter(employee => employee.isActive.toString().toLowerCase() === "no" && (!employee.inActivedAt || employee.inActivedAt === '')).map(employee => employee.employeeId);

        if (_.size(FoundInactivatedDateForActiveWorker)) {
            return [400, {
                ok: false,
                message: `Found inactivated date for is_active = Yes. Please remove inactivated date value for following employee Ids . \n [ ${Array.from(_.uniq(FoundInactivatedDateForActiveWorker)).join('\n')} ] `
            }]
        }

        if (_.size(FoundInactivatedDateForBlankActiveFlag)) {
            return [400, {
                ok: false,
                message: `Found inactivated date for blank is_active. Please add is_active flag value for following employee Ids . \n [ ${Array.from(_.uniq(FoundInactivatedDateForBlankActiveFlag)).join('\n')} ] `
            }]
        }

        if (_.size(InactivatedDateNotFound)) {
            return [400, {
                ok: false,
                message: `Inactivated date not found for is_active = No. Please add inactivated date value for following employee Ids . \n [ ${Array.from(_.uniq(InactivatedDateNotFound)).join('\n')} ] `
            }]
        }

        let dataLength = data.length;
        // let associatedClientJobs = await getJobsByClientID(clientId, siteId);
        const jobNotFound = [];

        let invalidAssignmentdates = [];

        // Check if requested job or site data valid as per the client and sites
        let associatedClientJobs = await getAllJobsByClientID(clientId);
        for (let worker of data) {
            const jobDetails = associatedClientJobs.find(
                job =>
                    job.job_name === worker.jobName.toLowerCase() &&
                    job.job_type === String(worker.roleType) &&
                    job.department_name === worker.departmentName.toLowerCase() &&
                    job.shift_name === worker.shiftName.toLowerCase() &&
                    job.site_name === worker.siteName.toLowerCase()
            );
            if (jobDetails) {
                worker.jobId = jobDetails.job_id;
                delete worker.jobName;
            } else {
                jobNotFound.push(`${worker.siteName} || ${worker.jobName} - ${worker.shiftName} - ${RoleType[worker.jobType]} - ${worker.departmentName}`);
            }
        }

        if (_.size(jobNotFound)) {
            return [400, {
                ok: false,
                message: `Invalid job combination for the site. \nPlease check the spelling and try again or contact the admin. \n [ ${Array.from(_.uniq(jobNotFound)).join('\n')} ] `
            }]
        }

        if (_.size(invalidAssignmentdates)) {
            let errorMessage = `Error: The following employees have invalid assignment dates where the assignment date is earlier than the start date. Please re-upload the sheet with corrected dates.\n\n`;
            errorMessage += `Employee ID | Start Date | Assignment Date\n`;
            errorMessage += `------------------------------------------\n`;

            return [400, {
                ok: false,
                message: `${errorMessage} ${Array.from(_.uniq(invalidAssignmentdates)).join('\n')}`
            }]
        }


        let emailList: string[] = [];
        for (let workerDetail of data) {
            emailList.push(workerDetail['email']);
            workerDetail['createdBy'] = userId;
            workerDetail['updatedBy'] = userId;
            workerDetail['updatedAt'] = new Date();
            workerDetail['clientId'] = clientId;
            workerDetail['agencyId'] = agencyId;
            workerDetail['isActive'] = "isActive" in workerDetail ? (workerDetail.isActive.toString().toLowerCase() === "no" ? false : true) : true;
            workerDetail['pensionOptOut'] = workerDetail['pensionOptOut'] ? (workerDetail.pensionOptOut.toString().toLowerCase() === "no" ? false : true) : true;
            workerDetail['transport'] = workerDetail['transport'] ? (workerDetail.transport.toString().toLowerCase() === "no" ? false : true) : false;
            workerDetail['otherAssignment'] = workerDetail['otherAssignment'] ? (workerDetail.otherAssignment.toString().toLowerCase() === "no" ? false : true) : false;
            workerDetail['studentVisa'] = workerDetail['studentVisa'] ? (workerDetail.studentVisa.toString().toLowerCase() === "no" ? false : true) : false;
            workerDetail['limitedHours'] = workerDetail['limitedHours'] ? (workerDetail.limitedHours.toString().toLowerCase() === "no" ? false : true) : false;
            workerDetail['type'] = workerType || WorkerTypes.TEMPORARY;
        }

        let emailExist = await validateWorkerUserEmail(emailList, userId);
        if (_.size(emailExist.existingWorkerUser)) {
            const errorObj = await dynamicErrorObject(ErrorResponse.WorkerEmailAlreadyExistsCustom, emailExist.existingWorkerUser.join(', \n'))
            return [409, errorObj]
        }

        if (_.size(emailExist.existingUserEmailList)) {
            const errorObj = await dynamicErrorObject(ErrorResponse.EmailAlreadyExistsCustom, emailExist.existingUserEmailList.join(', \n'))
            return [409, errorObj]
        }

        const entityManager = getManager(); // Use the entity manager for the transaction
        let workerDetailsForAutomatedMessage = [];
        let workAnniversaryWorkers = [];
        let message = null;

        // Start a single transaction
        await entityManager.transaction(async (transactionalEntityManager) => {
            if (_.size(emailExist.individualworkerUser)) {
                let today = await getTodaysDate();

                // Execute all update operations within a transaction
                await Promise.all(emailExist.individualworkerUser.map(async (worker) => {
                    let workerData = deepClone(_.find(data, { 'email': worker.email }));

                    // Prepare user update data
                    let dataToInsert = {
                        nationalInsuranceNumber: workerData.nationalInsuranceNumber,
                        name: workerData.firstName + ' ' + workerData.lastName,
                        countryCode: workerData.countryCode || '',
                        mobile: workerData.mobile || '',
                        createdBy: userId,
                        updatedBy: userId,
                        updatedAt: new Date(),
                    };

                    // Use transactionalEntityManager for updating user details
                    await updateUserTransaction(transactionalEntityManager, worker.user_id, dataToInsert);

                    // Prepare worker details and remove unnecessary fields
                    delete workerData.email;
                    delete workerData.departmentName;
                    delete workerData.shiftName;
                    delete workerData.roleType;
                    delete workerData.roleTypeName;
                    delete workerData.jobType;
                    delete workerData.siteName;
                    delete workerData.nationalInsuranceNumber;
                    workerData.updatedAt = new Date();
                    // Use transactionalEntityManager for updating worker details
                    await updateWorkersDataTransaction(transactionalEntityManager, workerData, worker.id);

                    // Collect worker data for later sending messages
                    let workerDataToSendMessage = {
                        id: worker.id,
                        agency_id: workerData.agencyId,
                        client_id: workerData.clientId,
                        start_date: workerData.startDate,
                        assignment_date: workerData.assignmentDate,
                        device_token: worker.device_token,
                        language: worker.language,
                    };

                    // Collect workers for anniversary message
                    workAnniversaryWorkers.push(workerDataToSendMessage);

                    // Collect workers whose start date is today for welcome messages
                    if (workerData.assignmentDate === today) {
                        workerDetailsForAutomatedMessage.push(workerDataToSendMessage);
                    }

                    // Remove processed worker data
                    data = data.filter((item) => item.email !== worker.email);
                }));
            }
        });

        // After transaction completes, Send `n` Annual Work Anniversary messages to worker if he has completed `n` working years.
        await Promise.all(workAnniversaryWorkers.map(async (worker) => {
            await sendWorkAnniversaryGreetingsIfEligible(worker);
        }));

        // Send FIRST_DAY_WELCOME_MESSAGE to workers whose start dates are today's date and who have previously self-registered themselves but have now been added by the agency.
        await prepareAndSendAutomatedEventMessages(
            workerDetailsForAutomatedMessage,
            AutomatedMessagesLabels.FIRST_DAY_WELCOME_MESSAGE
        );

        if (_.size(data)) {
            await addNewWorkers(data);
            await updateWorkerUserService(data, userId);

            // Prepare the email only if worker invite email is enabled
            let clientDetails = await getClientsById(clientId);
            if (clientDetails.worker_invite_email) {
                message = {
                    templateId: config.Sendgrid.WORKER_INVITE_EMAIL_TEMPLATE,
                    personalizations: []
                };

                data.forEach(worker => {
                    message.personalizations.push({
                        to: { email: worker.email },
                        dynamicTemplateData: {
                            subject_line: workerInviteEmailSubjectLine,
                            worker_name: worker.firstName
                        },
                    });
                });

            }
        }

        // Send the email outside the transaction, only after the transaction is successful
        if (message) { await sendTemplateEmailInBulk(message); }

        return [201, {
            'ok': true,
            message: dataLength + " " + MessageActions.CREATE_WORKERS,
        }]
    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            let errMessage = ErrorResponse.WorkerAlreadyExists;
            errMessage.message = errMessage.message + " (" + err.message + ")";
            return [409, errMessage]    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else if (ErrorCodes.wrongValueForField.includes(err.code)) {
            return [422, ErrorResponse.UnprocessableEntityForFile]
        }
        else if (err.error === "SENDGRID_BAD_REQUEST") {
            return [409, ErrorResponse.WorkerInviteEmailNotSent]
        }
        else {
            notifyBugsnag(err);
            return [422, ErrorResponse.UnprocessableEntity]
        }
    }
};

/**
 * Service to update the workers data.
 * @param  {UpdateWorkerDTO} requestPayload
 */
export const updateWorkerService = async (requestPayload: UpdateWorkerDTO, loggedInUser) => {
    try {
        await updateWorkers(requestPayload, loggedInUser);

        if (requestPayload.is_active === false && config.SEND_WORKERS_MOBILE_INACTIVITY_NOTIFICATION === 1) {
            await sendUnassignedWorkerMessages(requestPayload.workers);
        }

        return [200, {
            'ok': true,
            message: MessageActions.UPDATE_WORKERS
        }]
    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.WorkerAlreadyExists]    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

const resetWorkerComplianceApprovalsIfNeeded = async (worker, workerDetails, updatedAt, loggedInUser) => {
    // Fetch existing approvals for this worker
    const approvals = await getComplianceApprovalForWorkersIfExists([worker.id]);
    const approvalMap = new Map();
    approvals.forEach(a => {
        approvalMap.set(`${a.workerId}_${a.complianceCardId}`, a);
    });

    const workerToResetApproval = {
        workerId: worker.id,
        oldAccountNumber: workerDetails.accountNumber,
        oldSortCode: workerDetails.sortCode,
        oldPostCode: workerDetails.postCode,
        oldHouseNumber: workerDetails.houseNumber
    };

    // Check for postCode or houseNumber change (MULTIPLE_OCCUPANCY)
    const isPostCodeOrHouseNumberChanged = (
        (worker.postCode && worker.postCode !== workerDetails.postCode) ||
        (worker.houseNumber && worker.houseNumber !== workerDetails.houseNumber)
    );
    if (isPostCodeOrHouseNumberChanged && approvalMap.has(`${worker.id}_${ComplinaceCardsType.MULTIPLE_OCCUPANCY}`)) {
        const resetPayload = buildResetApprovalPayload(ComplinaceCardsType.MULTIPLE_OCCUPANCY, workerToResetApproval, updatedAt, loggedInUser.user_id);
        await insertComplianceApprovalTx(null, [resetPayload]);
    }

    // Check for accountNumber or sortCode change (BANKING)
    const isAccountNumberOrSortCodeChanged = (
        (worker.accountNumber && worker.accountNumber !== workerDetails.accountNumber) ||
        (worker.sortCode && worker.sortCode !== workerDetails.sortCode)
    );
    if (isAccountNumberOrSortCodeChanged && approvalMap.has(`${worker.id}_${ComplinaceCardsType.BANKING}`)) {
        const resetPayload = buildResetApprovalPayload(ComplinaceCardsType.BANKING, workerToResetApproval, updatedAt, loggedInUser.user_id);
        await insertComplianceApprovalTx(null, [resetPayload]);
    }
}

/**
 * Service to update worker.
 */
export const updateWorkerDetailService = async (id, requestPayload, loggedInUser) => {
    try {
        let workerDetails = await getWorkerByWorkerId(id);
        if (!workerDetails) {
            return [404, ErrorResponse.WorkerNotFound];
        }

        const isSortCodeEmpty = !requestPayload.sort_code; // Empty if an empty string
        const isAccountNumberEmpty = !requestPayload.account_number;
        if (isSortCodeEmpty !== isAccountNumberEmpty) return [401, ErrorResponse.MissingBankDetialsError];

        if (requestPayload.national_insurance_number === null || requestPayload.national_insurance_number === undefined || requestPayload.national_insurance_number.trim() === "") {
            requestPayload.national_insurance_number = `ni_prefix${uuid.v4()}`;
        }

        requestPayload.national_insurance_number = requestPayload.national_insurance_number.trim();

        // Checking the existing national insurance number exists with agency id
        if (!(requestPayload.national_insurance_number &&
            workerDetails.nationalInsuranceNumber &&
            requestPayload.national_insurance_number.toLowerCase() === workerDetails.nationalInsuranceNumber.toLowerCase())) {

            let availableNI;
            if (requestPayload.type == WorkerTypes.PERMANENT) {
                // Checking the existing national insurance number exists with client id for permanent worker
                availableNI = await getExistingNationalInsuranceWithClient([requestPayload.national_insurance_number], requestPayload.client_id);
            } else {
                // Checking the existing national insurance number exists with agency id
                availableNI = await getExistingNationalInsuranceWithAgency([requestPayload.national_insurance_number], requestPayload.agency_id);
            }

            if (_.size(availableNI)) {
                const errorObj = await dynamicErrorObject(ErrorResponse.NationalInsuranceNumberExistsErrorCustom, requestPayload.national_insurance_number)
                return [422, errorObj]
            }
        }

        let response = (await getJobListingForDropDownService(requestPayload.site_id))[1].job_list;
        let jobs = response.map(item => item.id);
        if (!jobs.includes(requestPayload.job_id)) {
            return [404, ErrorResponse.JobNotFound];
        }

        if (workerDetails.workersSupervisorStatus && requestPayload.workers_supervisor_status != undefined && workerDetails.workersSupervisorStatus != requestPayload.workers_supervisor_status) {
            const errorObj = await dynamicErrorObject(ErrorResponse.SupervisorStatusAlreadySet, workerDetails.workersSupervisorStatus)
            return [422, errorObj]
        }

        if (requestPayload.start_date > requestPayload.assignment_date) {
            return [404, ErrorResponse.InvalidWorkerAssignmentDates]
        }

        if (Object.keys(requestPayload).length > 0) {
            // Define field mappings between request and worker model
            const fieldMappings = {
                first_name: 'firstName',
                last_name: 'lastName',
                date_of_birth: 'dateOfBirth',
                post_code: 'postCode',
                nationality: 'nationality',
                orientation: 'orientation',
                country_code: 'countryCode',
                mobile: 'mobile',
                payroll_ref: 'payrollRef',
                employee_id: 'employeeId',
                job_id: 'jobId',
                workers_supervisor_status: 'workersSupervisorStatus',
                national_insurance_number: 'nationalInsuranceNumber',
                client_id: 'clientId',
                start_date: 'startDate',
                assignment_date: 'assignmentDate',
                agency_id: 'agencyId',
                is_active: 'isActive',
                transport: 'transport',
                other_assignment: 'otherAssignment',
                pension_opt_out: 'pensionOptOut',
                limited_hours: 'limitedHours',
                student_visa: 'studentVisa',
                house_number: 'houseNumber',
                sort_code: 'sortCode',
                account_number: 'accountNumber'
            };

            // Build update data object
            const updateWorkerData = Object.entries(fieldMappings).reduce((acc, [requestKey, workerKey]) => {
                if (requestKey in requestPayload) {
                    acc[workerKey] = requestKey === 'house_number'
                        ? stripEmojis(requestPayload[requestKey])
                        : requestPayload[requestKey];
                }
                return acc;
            }, {});

            if ((updateWorkerData["startDate"] || workerDetails.startDate) > (updateWorkerData["assignmentDate"] || workerDetails.assignmentDate)) {
                return [404, ErrorResponse.InvalidWorkerAssignmentDates]
            }

            // Handle documents update
            if ('documents' in requestPayload) {
                await updateUserHelper(workerDetails.userId, { documents: requestPayload.documents });
            }

            // Handle employee ID change
            if ('employee_id' in requestPayload && requestPayload.employee_id !== workerDetails.employeeId) {
                await updateTotalAgencyPayDataByEmployeeId(workerDetails.employeeId, {
                    employeeId: requestPayload.employee_id
                });
                updateWorkerData["historicalEmployeeIds"] = !workerDetails.historicalEmployeeIds ? JSON.stringify([requestPayload.employee_id]) : JSON.stringify(JSON.parse(workerDetails.historicalEmployeeIds).concat(requestPayload.employee_id));
            }

            const updatedAt = new Date();
            updateWorkerData["updatedBy"] = parseInt(loggedInUser.user_id);
            updateWorkerData["updatedAt"] = updatedAt;
            // Update worker details
            await updateWorkerDetail(id, updateWorkerData);
            const worker = await getWorkerByWorkerId(id);

            // Handle national insurance number update
            if ('national_insurance_number' in requestPayload) {
                const workerIds = (await getWorkersByNationalInsuranceNumber(workerDetails.nationalInsuranceNumber))
                    .map(object => parseInt(object.id));
                if (workerIds.length) {
                    await updateWorkerNationalInsuranceNumber(workerIds, requestPayload.national_insurance_number);
                }
            }

            // Update user record
            const userUpdateData = {
                name: `${worker.firstName} ${worker.lastName}`,
                countryCode: worker.countryCode,
                mobile: worker.mobile,
                clientId: worker.clientId,
                agencyId: worker.agencyId,
                nationalInsuranceNumber: worker.nationalInsuranceNumber,
                updatedBy: parseInt(loggedInUser.user_id),
                updatedAt: updatedAt,
                ...(('email' in requestPayload) && { email: requestPayload.email })
            };

            await updateUserHelper(worker.userId, userUpdateData);

            // Log pension opt out status
            // Check if there's a change in pensionOptOut
            if (workerDetails.pensionOptOut != requestPayload.pension_opt_out) {
                const newPensionStatusLog = {
                    workerId: parseInt(workerDetails.id),
                    from: workerDetails.pensionOptOut ? PensionStatus.OPTED_OUT : PensionStatus.OPTED_IN,
                    to: requestPayload.pension_opt_out ? PensionStatus.OPTED_OUT : PensionStatus.OPTED_IN,
                    createdBy: parseInt(loggedInUser.user_id)
                }
                await createPensionStatusLog(newPensionStatusLog);
            }

            await resetWorkerComplianceApprovalsIfNeeded(worker, workerDetails, updatedAt, loggedInUser);
        }

        if (requestPayload.national_insurance_number.includes('ni_prefix')) {
            await setNullNiNumberUser(JSON.stringify(requestPayload.national_insurance_number));
            await setNullNiNumberWorker(JSON.stringify(requestPayload.national_insurance_number));
        }

        return [200, {
            'ok': true,
            message: MessageActions.UPDATE_SINGLE_WORKERS
        }];
    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError && err.sqlMessage.includes("email_UNIQUE")) {
            return [409, ErrorResponse.EmailAlreadyExists]    // Return 409 if Email already exists in db
        }
        else if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.WorkerAlreadyExists]    // Return 409 if worker already exists
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

/**
 * Service to get worker listing.
 */
export const getWorkersListService = async (data: GetWorkersDTO, user_type_id, workersIdsForSearch = []) => {
    try {

        let response = []
        let whereClause: string;

        if (data.client_id && !data.agency_id && !data.site_id) {
            whereClause = `workers.client_id = :client_id `;
        } else if (data.client_id && data.agency_id && !data.site_id) {
            whereClause = `workers.client_id = :client_id AND workers.agency_id = :agency_id `;
        } else if (data.client_id && data.site_id && !data.agency_id) {
            whereClause = `workers.client_id = :client_id AND job_association.site_id = :site_id `;
        } else {
            whereClause = `workers.client_id = :client_id AND workers.agency_id = :agency_id AND job_association.site_id = :site_id `;
        }
        whereClause += data.type ? ` AND workers.type = :type` : '';
        whereClause += workersIdsForSearch.length ? ` AND workers.id IN (:workers_ids_for_search)` : '';
        data.sort_type = data.sort_by === "is_active" ? data.sort_type === "ASC" ? "DESC" : "ASC" : data.sort_type;

        let isLimitedView = false;
        if (data.type == WorkerTypes.TEMPORARY && (user_type_id == UserType.MESSAGE_ADMIN || user_type_id == UserType.CLIENT_ADMIN || user_type_id == UserType.CLIENT_SITE || user_type_id == UserType.CLIENT_REGIONAL)) {
            isLimitedView = true;
        }

        let whereClauseValue = { "client_id": data.client_id, "agency_id": data.agency_id, "site_id": data.site_id, "type": data.type, "workers_ids_for_search": workersIdsForSearch };
        response = await getWorkers(whereClause, whereClauseValue, data.page || 1, data.limit || 10, data.sort_by || "id", data.sort_type || "ASC", isLimitedView) || [];

        return [200, {
            ok: true,
            count: parseInt(response["count"]) || 0,
            workers: response,
        }]

    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.WorkerAlreadyExists];    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound];    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message];
        }
    }
}

/**
 * Search workers
 */
export const searchWorkersService = async (requestPayload: any, queryParams, user_type_id) => {
    try {

        if (requestPayload.all_workers) {
            return await getWorkersListService(queryParams, user_type_id);
        }

        let booleanFields = {
            under_twentyone: requestPayload.under_twentyone,
            returning_worker: requestPayload.returning_worker,
            under_twentytwo: requestPayload.under_twentytwo,
            within_twelveweeks: requestPayload.within_twelveweeks
        }

        let response = await searchWorkersList(
            requestPayload.first_name,
            requestPayload.last_name,
            requestPayload.email,
            requestPayload.employee_id,
            requestPayload.payroll_ref,
            requestPayload.pension_opt_out,
            requestPayload.other_assignment,
            requestPayload.app_downloaded,
            requestPayload.national_insurance_number,
            requestPayload.banking_details,
            requestPayload.limited_hours,
            requestPayload.student_visa,
            requestPayload.assignment_date,
            requestPayload.site_id,
            requestPayload.role_id,
            requestPayload.supervisor_status,
            requestPayload.los,
            requestPayload.temp_to_perm_leaver,
            booleanFields,
        );

        let workerList = response.map((worker) => worker.id);

        if (workerList.length) {
            return await getWorkersListService(queryParams, user_type_id, workerList);
        } else {
            return [200, {
                ok: true,
                count: 0,
                workers: [],
            }]
        }

    } catch (err) {
        notifyBugsnag(err);
        return [500, err.message];
    }
}


/**
 * Validate worker email with user table
 * @param  {string[]} emailList
 */
const validateWorkerEmailWithExistingUsers = async (emailList: string[]) => {
    return await getRequestedUserEmailCounts(emailList);
}

const validateNationalInsuranceNumber = async (nationalInsuranceNumber: string) => {
    return await nationalInsuranceNumberExistsHelper(nationalInsuranceNumber) ? true : false;
}

/**
 * Service to GET worker details by worker ID..
 */
export const getWorkerDetailsByWorkerIdService = async (workerId, loggedInUser) => {
    try {
        let whereClause = `workers.id = :worker_id `;
        let response = await getWorkerDetailsHelper(whereClause, { "worker_id": workerId });
        if (!response) {
            return [404, ErrorResponse.ResourceNotFound]
        }
        response.documents = JSON.parse(response.documents);

        // Validate if the worker belongs to the logged-in user (client or agency).
        let { user_id, agency_id, client_id } = response;

        const userTypeId = Number(loggedInUser.user_type_id);

        if ([UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.MESSAGE_ADMIN].includes(userTypeId)) {
            const loggedInClientId = Number(loggedInUser.client_id);
            if (!loggedInClientId || loggedInClientId !== Number(client_id)) {
                return [403, ErrorResponse.PermissionDenied];
            }
        }
        else if ([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE].includes(userTypeId)) {
            const loggedInUserId = Number(loggedInUser.user_id);
            const userDetails = await getUserById(loggedInUserId);

            const loggedInAgencyId = Number(userDetails.agency_id);
            if (!loggedInAgencyId || loggedInAgencyId !== Number(agency_id)) {
                return [403, ErrorResponse.PermissionDenied];
            }
        }

        //TODO: GET worker length of service data.
        let workerIds = _.map(await getWorkerIdFromUserIdAndAgencyId(user_id, agency_id), 'id')

        response.length_of_service = await getWorkerLengthOfServiceFromWorkerId(workerIds);

        //TODO: GET worker Shifts completed data.
        response.shift_completed = await getWorkerShiftsCompleted(workerIds);

        let appreciationData = await getWorkerAppreciationDataFromUserIdHelper(user_id, agency_id);

        let workerAppreciation = {
            "awards": appreciationData.map(object => object.appreciation.award).reduce((a, b) => a + b, 0),
            "badge": appreciationData.map(object => object.appreciation.badge).reduce((a, b) => a + b, 0) + await getCompletedTrainingCount(workerIds),
            "recognition": appreciationData.map(object => object.appreciation.recognition).reduce((a, b) => a + b, 0)
        }

        response.appreciation = workerAppreciation;

        response.job_name = `${response.job_name} - ${response.shift_name} - ${RoleType[response.job_type]} - ${response.department_name}`;
        response.under_twentyone = moment(new Date()).diff(moment(response.date_of_birth), 'years') < 21;
        response.under_twentytwo = moment(new Date()).diff(moment(response.date_of_birth), 'years') < 22;
        response.within_twelveweeks = moment(new Date()).diff(moment(response.assignment_date), 'weeks') < 12;
        response.pension_opt_out = Boolean(response.pension_opt_out);
        response.transport = Boolean(response.transport);
        response.other_assignment = Boolean(response.other_assignment);
        response.limited_hours = Boolean(response.limited_hours);
        response.student_visa = Boolean(response.student_visa);

        if (response.type == WorkerTypes.TEMPORARY && (loggedInUser.user_type_id == UserType.MESSAGE_ADMIN || loggedInUser.user_type_id == UserType.CLIENT_ADMIN || loggedInUser.user_type_id == UserType.CLIENT_SITE || loggedInUser.user_type_id == UserType.CLIENT_REGIONAL)) {
            response.date_of_birth = '';
            response.national_insurance_number = '';
            response.orientation = '';
            response.nationality = '';
            response.sort_code = null;
            response.account_number = null;
            response.post_code = response.partial_post_code;
            response.house_number = '';
        }

        return [200, { ok: true, data: response }]
    } catch (error) {
        notifyBugsnag(error);
        return [500, error.message]
    }
}

/**
 * Service for updating the worker account password.
 * @param  {} requestArgs
 * @param  {} password
 * @param  {} loggedInUser
 */
export const workerRegistationService = async (data, loggedInUser) => {
    try {
        //Confirming whether worker exists.
        let whereClause = `user.email = :email`;
        if (data.national_insurance_number) {
            delete data.national_insurance_number;
        }

        let whereClauseValue = { "email": data.email };
        let workerDetails = await getWorkerHelper(whereClause, whereClauseValue);
        if (!workerDetails) {
            return [404, ErrorResponse.WorkerIsNotRegistered]
        }

        //Fetching the worker id from the response object of the helper. 
        let { user_id } = workerDetails;

        // Send First Day Welcome Message to the workers who was already added by Agency BEFORE self-registration via mobile app and start_date = today.
        if (workerDetails.start_date === await getTodaysDate()) {
            await prepareAndSendAutomatedEventMessages([workerDetails], AutomatedMessagesLabels.FIRST_DAY_WELCOME_MESSAGE);
        }

        //Generating the hash of the plain password
        let salt = bcrypt.genSaltSync(bcryptSaltRound);
        let encodedPassword = bcrypt.hashSync(data.password, salt);

        //Updating the worker password.
        await updateUser(user_id, { password: encodedPassword, email: data.email });
        return [200, { ok: true, message: MessageActions.REGISTRATION_SUCCESS }]

    } catch (error) {
        notifyBugsnag(error);
        return [500, error.message]
    }
}

/**
 * Service for worker login.
 * @param  {} workerData
 */
export const workerLoginService = async (workerData) => {
    try {
        //Confirming the user existence.
        let whereClause = `user.email = :email`
        let workerDetails = await getWorkerHelper(whereClause, { "email": workerData.email })
        if (!workerDetails) {
            return [404, ErrorResponse.WorkerIsNotRegistered];
        }
        // Confirming the Password
        if (workerDetails.password && bcrypt.compareSync(workerData.password, workerDetails.password)) {

            //Updating the device token of the worker.
            await updateWorkerHelper(workerDetails.user_id, { deviceToken: workerData.device_token || null })

            let jwtWorkerData = {
                user_id: workerDetails.user_id,
                user_name: workerDetails.first_name + ' ' + workerDetails.last_name,
                user_type_id: UserType.AGENCY_WORKER,
            }

            return [200, {
                ok: true,
                user_id: workerDetails.user_id,
                user_type: "worker",
                worker_id: workerDetails.id,
                access_token: await jwt.sign(
                    jwtWorkerData,
                    config.JWT_TOKEN_KEY,
                    {
                        expiresIn: config.WORKER_ACCESS_TOKEN_EXPIRE_TIME,
                    }
                ),

                refresh_token: await jwt.sign(
                    jwtWorkerData,
                    config.JWT_TOKEN_KEY,
                    {
                        expiresIn: config.USER_REFRESH_TOKEN_EXPIRE_TIME,
                    }
                )
            }];

        }
        return [401, ErrorResponse.WorkerInvalidCredentials];
        //Generating the JWT authentication token and resend in login api.
    } catch (error) {
        notifyBugsnag(error);
        return [500, error.message]
    }
}

/**
 * Service for worker to upload the documents.
 * @param  {} workerData
 */
export const documentsUploadService = async (documents) => {
    try {
        let documentKeys = Object.keys(documents)
        let uploadedDocuments = {}
        let uuidList = []

        //Uploading the documents in the folder
        for (const key of documentKeys) {
            let uuidKey = uuid.v1() + path.extname(documents[key].name)
            uuidList.push(uuidKey)
            await uploadFileOnS3(config.BUCKET_NAME, config.DOCUMENTS_FOLDER, uuidKey, documents[key].mimetype, documents[key].data);
        }

        //Getting the url of the object uploaded.
        for (const key of documentKeys) {
            const iterator = documentKeys.indexOf(key);
            let url = config.BUCKET_URL + "/" + config.DOCUMENTS_FOLDER + "/" + uuidList[iterator];
            uploadedDocuments[key] = { name: documents[key].name, url, uuid: uuidList[iterator] };
        }

        return [200, { ok: true, documents: uploadedDocuments }]

    } catch (error) {
        notifyBugsnag(error);
        return [500, error.message]
    }
}

/**
 * Service to get the worker list without pagination support.
 * @param data
 */
export const getWorkersListWithoutPaginationService = async (data: GetWorkersDTO) => {
    try {
        let response = []
        let whereClause = {}
        if (data.client_id && !data.agency_id && !data.site_id) {
            whereClause = `workers.client_id = :client_id `;
        } else if (data.client_id && data.agency_id && !data.site_id) {
            whereClause = `workers.client_id = :client_id AND workers.agency_id = :agency_id `;
        } else if (data.client_id && data.site_id && !data.agency_id) {
            whereClause = `workers.client_id = :client_id AND job_association.site_id = :site_id `;
        } else {
            whereClause = `workers.client_id = :client_id AND workers.agency_id = :agency_id AND job_association.site_id = :site_id `;
        }
        whereClause += data.type ? ` AND workers.type = :type` : '';
        whereClause += " AND workers.is_active = 1";

        let whereClauseValue = { "client_id": data.client_id, "agency_id": data.agency_id, "site_id": data.site_id, "type": data.type }
        response = await getWorkersWithoutPagination(whereClause, whereClauseValue) || [];
        return [200, {
            ok: true,
            count: parseInt(response["count"]) || 0,
            workers: response,
        }]
    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.WorkerAlreadyExists];    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound];    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [400, ErrorResponse.BadRequestError];
        }
    }
}

export const getWorkerIdFromUserIdService = async (userId) => {
    //Get the list of worker-id(s) from the user id.

    let workerIds = await getWorkerIdfromUserId({ userId, isActive: true });
    let workerIdList = _.map(workerIds, 'id');

    if (!_.size(workerIdList)) {
        let workerId = await getWorkerIdfromUserId({ userId });
        workerIdList = _.map(workerId, 'id');
    }
    return [Math.max(...workerIdList), workerIdList];
}


/**
 * Service for fetching the worker profile from user-id.
 * @param userId
 */
export const workerProfileService = async (userId) => {
    /* Check whether the user is worker or not. if not then throw not worker not found. */
    let userDetails = await getUserById(userId)
    if (!userDetails) {
        return [404, ErrorResponse.UserNotFound]
    }

    let url = userDetails.resource ? userDetails.resource : config.BUCKET_URL + "/" + config.PROFILE_BUCKET_FOLDER + "/" + config.DEFAULT_IMAGE;
    let [lastAddedWorkerId, workerIdList] = await getWorkerIdFromUserIdService(userId);
    //Fetch the basic worker information like worker name, post-code and save in the response object and training information and right to work documents.
    //TODO: Fetching of the documents from user is remaining.
    let response = await getWorkerUserDetails(userId, lastAddedWorkerId)
    response.url = url;

    response.documents = response.documents ? JSON.parse(response.documents) : {}
    // Worker length of service from worker ID.
    response.length_of_service = await getWorkerLengthOfServiceFromWorkerId(workerIdList);

    //Derive Shifts completed from time and attendance data.
    response.shift_completed = await getWorkerShiftsCompleted(workerIdList);

    let whereClauseString = `training.is_training_completed = 1 and training.worker_id IN(:worker_id_list)`
    let whereClauseValue = { "worker_id_list": workerIdList }
    //TODO: Add worker training data.
    response.training = await getWorkerTrainingData(whereClauseString, whereClauseValue);

    let workerDetails = await getDetailsWorkerId(lastAddedWorkerId);
    let appreciationData = await getWorkerAppreciationDataFromUserIdHelper(userId);

    const { lastPerformance, secondLastPerformance } = await getLastTwoPerformancesByWorkerId(lastAddedWorkerId)
    response.worker_performance = lastPerformance;
    response.worker_performance_previous = secondLastPerformance;

    let workerAppreciation = {
        "awards": appreciationData.map(object => object.appreciation.award).reduce((a, b) => a + b, 0),
        "badge": appreciationData.map(object => object.appreciation.badge).reduce((a, b) => a + b, 0) + await getCompletedTrainingCount(workerIdList),
        "kudos": appreciationData.map(object => object.appreciation.recognition).reduce((a, b) => a + b, 0)
    }
    response.appreciation = workerAppreciation;
    response.transport = Boolean(response.transport);
    response.pension_opt_out = Boolean(response.pension_opt_out);
    response.client_name = workerDetails.client_id ? (await getClientsById(workerDetails.client_id)).name : '';
    response.agency_name = workerDetails.agency_id ? (await getAgencyById(workerDetails.agency_id)).name : '';
    response = { ...response, ...workerDetails }

    return [200, { ok: true, data: response }]
}

/**
 * Service for fetching the worker profile from user-id.
 * @param userId
 */
export const workerProfileServiceV2 = async (userId) => {
    /* Check whether the user is worker or not. if not then throw not worker not found. */
    let userDetails = await getUserById(userId)
    if (!userDetails) {
        return [404, ErrorResponse.UserNotFound]
    }

    let url = userDetails.resource ? userDetails.resource : config.BUCKET_URL + "/" + config.PROFILE_BUCKET_FOLDER + "/" + config.DEFAULT_IMAGE;
    let [lastAddedWorkerId, workerIdList] = await getWorkerIdFromUserIdService(userId);
    //Fetch the basic worker information like worker name, post-code and save in the response object and training information and right to work documents.
    //TODO: Fetching of the documents from user is remaining.
    let response = await getWorkerUserDetails(userId, lastAddedWorkerId)
    response.url = url;

    response.documents = response.documents ? JSON.parse(response.documents) : {}
    // Worker length of service from worker ID.
    response.length_of_service = await getWorkerLengthOfServiceFromWorkerId(workerIdList);

    //Derive Shifts completed from time and attendance data.
    response.shift_completed = await getWorkerShiftsCompleted(workerIdList);

    let whereClauseString = `training.is_training_completed = 1 and training.worker_id IN(${workerIdList})`
    //TODO: Add worker training data.
    response.training = await getWorkerTrainingData(whereClauseString);

    let workerDetails = await getDetailsWorkerId(lastAddedWorkerId);
    let appreciationData = await getWorkerAppreciationDataFromUserIdHelper(userId);

    const { lastPerformance, secondLastPerformance } = await getLastTwoPerformancesByWorkerId(lastAddedWorkerId)
    response.worker_performance = lastPerformance;
    response.worker_performance_previous = secondLastPerformance;

    let workerAppreciation = {
        "awards": appreciationData.map(object => object.appreciation.award).reduce((a, b) => a + b, 0),
        "badge": appreciationData.map(object => object.appreciation.badge).reduce((a, b) => a + b, 0) + await getCompletedTrainingCount(workerIdList),
        "recognition": appreciationData.map(object => object.appreciation.recognition).reduce((a, b) => a + b, 0)
    }
    response.appreciation = workerAppreciation;
    response.transport = Boolean(response.transport);
    response.pension_opt_out = Boolean(response.pension_opt_out);

    let clientDetails, agencyDetails;
    if (workerDetails.client_id) {
        clientDetails = await getClientsById(workerDetails.client_id);
    }
    response.client_name = clientDetails ? clientDetails.name : '';
    response.client_logo = clientDetails ? clientDetails.resource : '';

    if (workerDetails.agency_id) {
        agencyDetails = await getAgencyById(workerDetails.agency_id);
    }
    response.agency_name = agencyDetails ? agencyDetails.name : '';
    response.agency_logo = agencyDetails ? agencyDetails.resource : '';

    response = { ...response, ...workerDetails }

    return [200, { ok: true, data: response }]
}

export const updateWorkerUserService = async (data, userId) => {
    // List of national insurance numbers.
    let nationalInsuranceNumberList = []

    //Generating list of object with worker_id and national insurance number.
    data.map((dataElement) => {
        nationalInsuranceNumberList.push(dataElement.nationalInsuranceNumber)
    })

    let userNationalInsuranceNumberList = await getUserIdByNationalInsuranceNumber(nationalInsuranceNumberList);

    let userExistNationalInsuranceNumberList = userNationalInsuranceNumberList.map(iterator => {
        return iterator.nationalInsuranceNumber
    })

    let existingUserData = data.filter((dataElement) => {
        return userExistNationalInsuranceNumberList.includes(dataElement.nationalInsuranceNumber);
    });

    data = data.filter((dataElement) => {
        return !userExistNationalInsuranceNumberList.includes(dataElement.nationalInsuranceNumber);
    });


    let userDetailNIstr = []
    if (_.size(existingUserData)) {
        let updateUserEmailWhereClauseString = ``;
        let updateUserNameWhereClauseString = ``;
        existingUserData.forEach(iterator => {
            updateUserEmailWhereClauseString += ` WHEN national_insurance_number = "${iterator.nationalInsuranceNumber}" THEN "${iterator.email}" `;
            updateUserNameWhereClauseString += ` WHEN national_insurance_number = "${iterator.nationalInsuranceNumber}" THEN "${iterator.firstName} ${iterator.lastName}" `;
        });

        userExistNationalInsuranceNumberList.forEach((element) => {
            userDetailNIstr.push(JSON.stringify(element))
        })

        await bulkUpdateUserDetails(updateUserEmailWhereClauseString, updateUserNameWhereClauseString, userDetailNIstr);
    }



    let usersInsert = []
    usersInsert = data.map(dataElement => {
        return {
            userTypeId: UserType.AGENCY_WORKER.toString(),
            nationalInsuranceNumber: dataElement.nationalInsuranceNumber,
            name: dataElement.firstName + ' ' + dataElement.lastName,
            email: dataElement.email,
            countryCode: dataElement.countryCode || '',
            mobile: dataElement.mobile || '',
            createdBy: userId,
            createdAt: new Date()
        }
    })
    await addWorkerUserInBulk(usersInsert);

    let userNationalInsuranceNumberListAfterInserted = await getUserIdByNationalInsuranceNumber(nationalInsuranceNumberList);

    let str = []
    nationalInsuranceNumberList.forEach((element) => {
        str.push(JSON.stringify(element))
    })

    let updateWhereClauseString = ``;
    userNationalInsuranceNumberListAfterInserted.forEach(iterator => {
        updateWhereClauseString += ` WHEN national_insurance_number = "${iterator.nationalInsuranceNumber}" THEN ${iterator.id} `
    })

    await bulkUpdateUserId(updateWhereClauseString, str)

    if (_.size(str)) {
        await setNullNiNumberUser(str);
        await setNullNiNumberWorker(str);
    }
}

/**
 * Service to update worker user.
 */
export const updateWorkerUserServiceTransaction = async (transactionalEntityManager, data, userId) => {
    let nationalInsuranceNumberList = data.map(dataElement => dataElement.nationalInsuranceNumber);
    let userNationalInsuranceNumberList = await getUserIdByNationalInsuranceNumber(nationalInsuranceNumberList);

    let userExistNationalInsuranceNumberList = userNationalInsuranceNumberList.map(iterator => iterator.nationalInsuranceNumber);
    let existingUserData = data.filter(dataElement => userExistNationalInsuranceNumberList.includes(dataElement.nationalInsuranceNumber));
    data = data.filter(dataElement => !userExistNationalInsuranceNumberList.includes(dataElement.nationalInsuranceNumber));

    // Update existing users
    if (_.size(existingUserData)) {
        let updateUserEmailWhereClauseString = '';
        let updateUserNameWhereClauseString = '';

        existingUserData.forEach(iterator => {
            updateUserEmailWhereClauseString += ` WHEN national_insurance_number = "${iterator.nationalInsuranceNumber}" THEN "${iterator.email}" `;
            updateUserNameWhereClauseString += ` WHEN national_insurance_number = "${iterator.nationalInsuranceNumber}" THEN "${iterator.firstName} ${iterator.lastName}" `;
        });

        let userDetailNIstr = existingUserData.map(iterator => JSON.stringify(iterator.nationalInsuranceNumber));
        await bulkUpdateUserDetailsTransaction(transactionalEntityManager, updateUserEmailWhereClauseString, updateUserNameWhereClauseString, userDetailNIstr);
    }

    // Insert new users
    let usersInsert = data.map(dataElement => ({
        userTypeId: UserType.AGENCY_WORKER.toString(),
        nationalInsuranceNumber: dataElement.nationalInsuranceNumber,
        name: dataElement.firstName + ' ' + dataElement.lastName,
        email: dataElement.email,
        countryCode: dataElement.countryCode || '',
        mobile: dataElement.mobile || '',
        createdBy: userId,
        createdAt: new Date()
    }));

    await addWorkerUserInBulkTransaction(transactionalEntityManager, usersInsert);

    // Update user IDs in the worker records
    let userNationalInsuranceNumberListAfterInserted = await getUserIdByNationalInsuranceNumber(nationalInsuranceNumberList);
    let updateWhereClauseString = ``;

    userNationalInsuranceNumberListAfterInserted.forEach(iterator => {
        updateWhereClauseString += ` WHEN national_insurance_number = "${iterator.nationalInsuranceNumber}" THEN ${iterator.id} `;
    });

    await bulkUpdateUserIdTransaction(transactionalEntityManager, updateWhereClauseString, nationalInsuranceNumberList);

    // Handle null national insurance number
    if (_.size(nationalInsuranceNumberList)) {
        await setNullNiNumberUserTransaction(transactionalEntityManager, nationalInsuranceNumberList);
        await setNullNiNumberWorkerTransaction(transactionalEntityManager, nationalInsuranceNumberList);
    }
};

/**
 * Service to update worker user profile.
 */
export const updateWorkerProfileService = async (userId: string, payload) => {

    let object = payload;
    let userDetails = await getUserById(userId);
    if (payload.documents && Object.keys(payload.documents).length > 0) {
        if (userDetails.documents !== null) {
            Object.keys(payload.documents).forEach(key => {
                if (JSON.parse(userDetails.documents)[key]) {
                    throw new BadRequestError("BAD_REQUEST", "File already uploaded");
                }
            })
            object = { documents: { ...JSON.parse(userDetails.documents), ...payload.documents } }
        } else {
            object = { documents: { ...payload.documents } }
        }
    }
    if (payload.resource) {
        object.resource = payload.resource
    }

    let updateResponce = await updateWorkerProfile(userId, object);
    if (!updateResponce.affected) {
        return [404, ErrorResponse.WorkerNotFound]        // Return 404 if any foreign key contraint is not available in DB
    }
    return [200, {
        ok: true,
        message: MessageActions.UPDATE_WORKER_PROFILE,
    }];
};


/**
 * Service to GET worker group.
 */
export const getWorkerGroupListService = async (data) => {
    let workerGroupDetails = await getAllWorkerGroup(data);

    let shifts = [];              //List of shifts
    let jobs = [];                //List of jobs
    let departments = [];         //List of dipartments

    workerGroupDetails.forEach((data) => {
        shifts.push({
            "id": data.shift_id,
            "name": data.shift_name
        })
        jobs.push({
            "id": data.job_id,
            "name": data.job_name
        })
        departments.push({
            "id": data.department_id,
            "name": data.department_name
        })
    });

    // Remove duplicate elements
    shifts = Object.values(shifts.reduce((acc, cur) => Object.assign(acc, { [cur.id]: cur }), {}))
    jobs = Object.values(jobs.reduce((acc, cur) => Object.assign(acc, { [cur.id]: cur }), {}))
    departments = Object.values(departments.reduce((acc, cur) => Object.assign(acc, { [cur.id]: cur }), {}))

    return [200, {
        ok: true,
        data: {
            shifts,
            jobs,
            departments
        }
    }];
}

/**
 * Service to GET worker LOS from worker id.
 * workerIds: Array of worker IDs
 */
export const getWorkerLengthOfServiceFromWorkerId = async (workerIds) => {
    let maxWorkerId = Math.max(...workerIds);
    let lengthOfServiceData = await getWorkerLengthOfServiceByWorkerId(maxWorkerId);
    return await getDurationBetweenDates(lengthOfServiceData.assignment_date, lengthOfServiceData.end_date);
};

/**
 * Service to GET worker training details.
 */
export const trackWorkerTrainingService = async (messageId, data, loggedInUser) => {
    let message = await getMessageDetailsModel(messageId);
    if (!message) return [404, ErrorResponse.ResourceNotFound]

    // Get list of workers who recieved the particular message
    // To check loggedIn worker has access to read training message
    let workerIdList = await getWorkerIdFromMessageId(messageId);
    const latestWorkerId = Math.max(...(await getWorkerIdfromUserId({ userId: loggedInUser.user_id })).map(({ id }) => id));
    if (!latestWorkerId) {
        return [404, ErrorResponse.WorkerNotFound]
    }

    if (!workerIdList.includes(latestWorkerId)) {
        return [403, ErrorResponse.PermissionDenied]
    }

    if (data.is_training_completed == null && data.require_more_training == null) {
        return [400, ErrorResponse.BadRequestError]
    }
    let updateClause = ``;
    if (data.is_training_completed) {
        updateClause = `SET wt.is_training_completed = ${data.is_training_completed}, wt.training_completed_at = '${new Date().toISOString().slice(0, 19).replace('T', ' ')}',
                wt.updated_by = ${loggedInUser.user_id}, wt.updated_at = '${new Date().toISOString().slice(0, 19).replace('T', ' ')}'`
        await trackWorkerTrainingHelper(updateClause, messageId, loggedInUser)
    }
    else {

        updateClause = `SET wt.require_more_training = ${data.require_more_training}, wt.require_training_updated_at = '${new Date().toISOString().slice(0, 19).replace('T', ' ')}',
                wt.updated_by = ${loggedInUser.user_id}, wt.updated_at = '${new Date().toISOString().slice(0, 19).replace('T', ' ')}'`

        await trackWorkerTrainingHelper(updateClause, messageId, loggedInUser)

        //Fetching the site ID from the message ID.
        let { site_id } = await getMessageDetailsModel(messageId);

        //Fetching the agency ID from the message ID and user ID.
        let { agency_id, worker_id, first_name, last_name, training_name } = await getWorkerDetailsByMessageIdAndUserId(messageId, loggedInUser.user_id);


        //Fetching the site admins email list.
        let siteAdminEmails = await getAdminEmailsFromSiteId(site_id);

        //Mapping the emails of the site admin from array of objects to array.
        let adminEmailList = siteAdminEmails.map(obj => obj.email)

        //Fetching the agency admin email from the agency ID.
        let { email: agency_admin_email } = await getAgencyAdminEmailByAgencyId(agency_id);

        //Preparing the list of email ID to send the email.
        adminEmailList.push(agency_admin_email)

        adminEmailList.forEach(async email => {
            let message = {
                toEmailId: email,
                templateId: config.Sendgrid.BOOKING_NOTIFICATION_EMAIL_TEMPLATE,
                dynamicTemplateData: {
                    subject_line: `Needs more Training`,
                    booking_message: `Worker ${first_name} ${last_name} | ${worker_id} has identified that they need more training with ${training_name}.`
                },
            };
            sendTemplateEmail(message);
        })

    }
    return [200, { ok: true }]
}


/**
 * Get nationality of the workers
 * @param  {any} requestArgs
 */
export const getWorkersNationalityService = async (requestArgs: any) => {

    const { workerType, ...otherArgs } = requestArgs;
    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(otherArgs);

    let nationalities = await getNationalityOfWorkers(whereClause, whereClauseValue, workerType);

    return [200, {
        ok: true,
        nationalities: nationalities.map((obj) => {
            return obj.nationality;
        })
    }];

}


/**
 * Delete worker account
 * - Reset password to null so worker can't directly login to the system
 * - Worker can again signup to the system to use the mobile application
 * @param  {any} requestArgs
 */
export const deleteWorkerAccountService = async (loggedInUser) => {

    let result = await revokeUserProfileAccessHelper(loggedInUser.user_id, loggedInUser);
    await updateWorkerHelper(loggedInUser.user_id, { deviceToken: null })

    return result.affected ? [200, {
        ok: true
    }] : [404, ErrorResponse.WorkerNotFound];
}

/**
 * Service for create worker with mobile APP.
 * @param  {} data
 * @param  {} loggedInUser
 */
export const workerRegistationServiceV2 = async (data) => {
    try {
        //Confirming whether worker exists.
        let whereClause = `user.email = :email`;

        if (data.national_insurance_number) {
            delete data.national_insurance_number;
        }

        let whereClauseValue = { "email": data.email };
        let workerDetails = await getWorkerHelper(whereClause, whereClauseValue);
        if (!workerDetails) {
            let userId;

            //Generating the hash of the plain password
            let salt = bcrypt.genSaltSync(bcryptSaltRound);
            let encodedPassword = bcrypt.hashSync(data.password, salt);
            data.password = encodedPassword;
            ({ id: userId } = await createWorkerUser(data));

            let response = await addNewWorker(data, userId, userId);
            let jwtWorkerData = {
                user_id: userId,
                user_name: data.first_name + ' ' + data.surname,
                user_type_id: UserType.AGENCY_WORKER,
            }

            return [201, {
                ok: true,
                user_id: userId,
                user_type: "worker",
                worker_id: parseInt(response.id),
                access_token: await jwt.sign(
                    jwtWorkerData,
                    config.JWT_TOKEN_KEY,
                    {
                        expiresIn: config.WORKER_ACCESS_TOKEN_EXPIRE_TIME,
                    }
                ),

                refresh_token: await jwt.sign(
                    jwtWorkerData,
                    config.JWT_TOKEN_KEY,
                    {
                        expiresIn: config.USER_REFRESH_TOKEN_EXPIRE_TIME,
                    }
                ),
                message: MessageActions.CREATE_WORKER,
            }];


        } else if (workerDetails.password) {
            return [409, ErrorResponse.WorkerPasswordAlreadyExists]     // Return 409 if worker password already exists
        }

        //Fetching the worker id from the response object of the helper. 
        let { user_id } = workerDetails;

        // remove all automated messages which were generated in between duration of 'bulk upload' and 'mobile app registration day(today)'.
        await removeAutomatedMessagesWorkerAssociation(workerDetails.id);

        // Send `n` Annual Work Anniversary messages to worker if he has completed `n` working years.
        await sendWorkAnniversaryGreetingsIfEligible(workerDetails);

        // Send First Day Welcome Message to the workers who was already added by Agency BEFORE self-registration via mobile app AND whose start date is today's date
        if (workerDetails.start_date === await getTodaysDate()) {
            await prepareAndSendAutomatedEventMessages([workerDetails], AutomatedMessagesLabels.FIRST_DAY_WELCOME_MESSAGE);
        }

        //Generating the hash of the plain password
        let salt = bcrypt.genSaltSync(bcryptSaltRound);
        let encodedPassword = bcrypt.hashSync(data.password, salt);

        //Updating the worker password.
        await updateUser(user_id, { password: encodedPassword, email: data.email });

        let jwtWorkerData = {
            user_id: user_id,
            user_name: workerDetails.first_name + ' ' + workerDetails.last_name,
            user_type_id: UserType.AGENCY_WORKER,
        }

        return [200, {
            ok: true,
            user_id: user_id,
            user_type: "worker",
            worker_id: workerDetails.id,
            access_token: await jwt.sign(
                jwtWorkerData,
                config.JWT_TOKEN_KEY,
                {
                    expiresIn: config.WORKER_ACCESS_TOKEN_EXPIRE_TIME,
                }
            ),

            refresh_token: await jwt.sign(
                jwtWorkerData,
                config.JWT_TOKEN_KEY,
                {
                    expiresIn: config.USER_REFRESH_TOKEN_EXPIRE_TIME,
                }
            ),
            message: MessageActions.REGISTRATION_SUCCESS
        }];
    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.WorkerAlreadyExists]    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

/**
 * Service to update worker hours.
 */
export const updateWorkerDetailServiceV2 = async (loggedInUser, requestPayload) => {
    try {
        let workerIds = await getWorkerIdfromUserId({ userId: loggedInUser.user_id });
        let workerIdList = _.map(workerIds, 'id');
        let maxWorkerId = Math.max(...workerIdList);
        if (!maxWorkerId) {
            return [404, ErrorResponse.WorkerNotFound]
        }
        let updateWorkerData = {
            availability: requestPayload.availability,
            hours: requestPayload.hours,
        }

        await updateWorkerDetail(maxWorkerId, updateWorkerData);
        return [200, {
            'ok': true,
            message: MessageActions.UPDATE_SINGLE_WORKERS
        }]
    } catch (err) {
        notifyBugsnag(err);
        return [500, err.message]
    }
}

/**
 * Service to update worker language code.
 */
export const updateWorkerLanguageCodeService = async (loggedInUser, requestPayload) => {
    try {
        let workerIds = await getWorkerIdfromUserId({ userId: loggedInUser.user_id });
        let workerIdList = _.map(workerIds, 'id');
        let maxWorkerId = Math.max(...workerIdList);
        if (!maxWorkerId) {
            return [404, ErrorResponse.WorkerNotFound]
        }
        let updateWorkerData = {
            language: requestPayload.language
        }

        await updateWorkerDetail(maxWorkerId, updateWorkerData);
        return [200, {
            'ok': true,
            message: MessageActions.UPDATE_SINGLE_WORKERS
        }]
    } catch (err) {
        notifyBugsnag(err);
        return [500, err.message]
    }
}

/**
 *  Remove Worker to Automated message association from `message_receiver_workers` table
 */
export const removeAutomatedMessagesWorkerAssociation = async (workerId) => {
    let messageReceiverWorkersDetails = await getWorkerAutomatedMessageInfoToRemove(workerId);
    if (messageReceiverWorkersDetails.length) {
        let messageAssociationIds = [];
        let messageReceiver;

        messageReceiverWorkersDetails.forEach(async (item) => {
            messageAssociationIds.push(item.id);

            messageReceiver = JSON.parse(item.receiver)
            if (Array.isArray(messageReceiver) && messageReceiver.length > 0 && messageReceiver[0].type === "workers" && Array.isArray(messageReceiver[0].data)) {
                let index = messageReceiver[0].data.indexOf(item.worker_id);
                if (index !== -1) {
                    messageReceiver[0].data.splice(index, 1);
                }
            }
            await updateSystemMessage(item.message_id, { receiver: messageReceiver });
        });

        if (messageAssociationIds.length) {
            await deleteMessageReceiverWorkersRawsById(messageAssociationIds);
        }
    };
}

const buildResetApprovalPayload = (complianceCardId, worker, updatedAt, loggedInUserId) => ({
    complianceCardId,
    workerId: worker.workerId,
    isReset: true,
    clientApprovalStatus: 0,
    clientApprovalBy: loggedInUserId,
    clientApprovalAt: updatedAt,
    agencyApprovalStatus: 0,
    agencyApprovalBy: loggedInUserId,
    agencyApprovalAt: updatedAt,
    accountNumber: worker.oldAccountNumber,
    sortCode: worker.oldSortCode,
    postCode: worker.oldPostCode,
    houseNumber: worker.oldHouseNumber,
    createdAt: updatedAt,
    updatedAt: updatedAt
});


/**
 * Service to update bulk workers data.
 * @param  {} workersToUpdateUsingEmpId // workers with existing employeeIds
 * @param  {} workersToUpdateUsingEmail // workers with existing emails and new employeeIds are included
 * @param  {} requestArgs
 * @param  {} userId
 */
export const updateBulkWorkers = async (workersToUpdateUsingEmpId, workersToUpdateUsingEmail, requestArgs, userId) => {
    try {
        if (workersToUpdateUsingEmail.length > 0) {
            workersToUpdateUsingEmail = workersToUpdateUsingEmail.map(worker => ({
                ...worker,
                isReturningWorker: true
            }));
        }
        let data = workersToUpdateUsingEmpId.concat(workersToUpdateUsingEmail);


        // Checking the worker exists with employee id
        let whereClause: string = `workers.client_id = :client_id `;
        if (requestArgs.agency_id) {
            whereClause += `AND workers.agency_id = :agency_id `;
        }
        whereClause += requestArgs.type ? ` AND workers.type = :type` : ` AND workers.type = :type_temporary`;

        let whereClauseValue = { "client_id": requestArgs.client_id, "agency_id": requestArgs.agency_id, "type": requestArgs.type, "type_temporary": WorkerTypes.TEMPORARY };
        let { availableEmployee } = await getExistingEmployeeId(whereClause, whereClauseValue);

        // Check If email already exist in DB
        const workerEmailList = workersToUpdateUsingEmpId.map(worker => worker.email).filter(email => email !== '');
        let existingEmailUser = workerEmailList.length ? await getUserByEmailList(workerEmailList) : [];
        if (_.size(existingEmailUser)) {
            const existingEmails = existingEmailUser.map(item => item.email).join(', \n');
            const errorObj = await dynamicErrorObject(ErrorResponse.EmailAlreadyExistsCustom, existingEmails)
            return [409, errorObj]
        }
        // Check if NationalInsuranceNumber exists in DB
        const workernationalInsuranceNumberList = data.map(worker => worker.nationalInsuranceNumber).filter(nationalInsuranceNumber => nationalInsuranceNumber !== '');


        let availableNI
        if (requestArgs.type == WorkerTypes.PERMANENT) {
            // Checking the existing national insurance number exists with client id for permanent worker
            availableNI = workernationalInsuranceNumberList.length ? await getExistingNationalInsuranceWithClient(workernationalInsuranceNumberList, parseInt(requestArgs.client_id) || null) : [];
        } else {
            // Checking the existing national insurance number exists with agency id
            availableNI = workernationalInsuranceNumberList.length ? await getExistingNationalInsuranceWithAgency(workernationalInsuranceNumberList, parseInt(requestArgs.agency_id) || null) : [];
        }

        if (_.size(availableNI)) {
            let availableNIList = _.map(availableNI, 'nationalInsuranceNumber');
            const errorObj = await dynamicErrorObject(ErrorResponse.NationalInsuranceNumberExistsErrorCustom, availableNIList)
            return [422, errorObj]
        }

        // Differenciate 'Existing workers' and 'New Workers' 
        const availableEmployeeIds = new Set(availableEmployee.map(worker => worker.employeeId));
        let employeeNotFound = [];
        let existingWorkers = workersToUpdateUsingEmail;
        for (let worker of workersToUpdateUsingEmpId) {
            if (!availableEmployeeIds.has(worker.employeeId)) {
                employeeNotFound.push(worker.employeeId);
            } else {
                existingWorkers.push(worker);
            }
        }

        const FoundInvalidWorkersToChangeSupervisorStatus = [];
        const invalidAssignmentdates = [];
        const employeeStatusMap = new Map();
        const employeeStartDateMap = new Map();
        const employeeAssignmentDateMap = new Map();

        // Preprocess availableEmployee to create a map of employeeId to workersSupervisorStatus
        availableEmployee.forEach(existingWorker => {
            employeeStatusMap.set(existingWorker.employeeId, existingWorker.workersSupervisorStatus);
            employeeStartDateMap.set(existingWorker.employeeId, existingWorker.startDate);
            employeeAssignmentDateMap.set(existingWorker.employeeId, existingWorker.assignmentDate);

            employeeStatusMap.set(existingWorker.email, existingWorker.workersSupervisorStatus);
            employeeStartDateMap.set(existingWorker.email, existingWorker.startDate);
            employeeAssignmentDateMap.set(existingWorker.email, existingWorker.assignmentDate);

        });

        // Iterate through data and check for invalid supervisor status request
        existingWorkers.forEach(wkr => {
            if (wkr.workersSupervisorStatus && (employeeStatusMap.get(wkr.employeeId) && wkr.workersSupervisorStatus !== employeeStatusMap.get(wkr.employeeId))) {
                FoundInvalidWorkersToChangeSupervisorStatus.push(`${wkr.employeeId} - ${employeeStatusMap.get(wkr.employeeId)}`);
            }

            if (wkr.workersSupervisorStatus && (employeeStatusMap.get(wkr.email) && wkr.workersSupervisorStatus !== employeeStatusMap.get(wkr.email))) {
                FoundInvalidWorkersToChangeSupervisorStatus.push(`${wkr.employeeId} - ${employeeStatusMap.get(wkr.email)}`);
            }

            if (wkr.startDate || wkr.assignmentDate) {
                const startDate = wkr.startDate || employeeStartDateMap.get(wkr.employeeId) || employeeStartDateMap.get(wkr.email);
                const assignmentDate = wkr.assignmentDate || employeeAssignmentDateMap.get(wkr.employeeId) || employeeAssignmentDateMap.get(wkr.email);
                if (startDate > assignmentDate) {
                    invalidAssignmentdates.push(`${wkr.employeeId.toString().padEnd(10)} | ${startDate} | ${assignmentDate}`)
                }
            }
        });

        if (_.size(FoundInvalidWorkersToChangeSupervisorStatus)) {
            return [400, {
                ok: false,
                message: `The supervisor status for the following employee IDs in the sheet is already set and cannot be modified at this time. \n [Employee Id - Current Status] \n ${Array.from(_.uniq(FoundInvalidWorkersToChangeSupervisorStatus)).join('\n')}`
            }]
        }

        if (_.size(invalidAssignmentdates)) {
            let errorMessage = `Error: The following employees in the sheet have invalid start date or assignment date where the assignment date is earlier than the start date. Please re-upload the sheet with corrected dates.\n\n`;
            errorMessage += `Employee ID | Start Date | Assignment Date\n`;
            errorMessage += `------------------------------------------\n`;

            return [400, {
                ok: false,
                message: `${errorMessage} ${Array.from(_.uniq(invalidAssignmentdates)).join('\n')}`
            }]
        }

        // Handle bulk worker inactivate scenarios
        let FoundInactivatedDateForActiveWorker = existingWorkers.filter(employee => employee.isActive.toString().toLowerCase() === "yes" && employee.inActivedAt && employee.inActivedAt !== '').map(employee => employee.employeeId);
        let FoundInactivatedDateForBlankActiveFlag = existingWorkers.filter(employee => (employee.isActive === null || employee.isActive === '') && employee.inActivedAt && employee.inActivedAt !== '').map(employee => employee.employeeId);
        let InactivatedDateNotFound = existingWorkers.filter(employee => employee.isActive.toString().toLowerCase() === "no" && (!employee.inActivedAt || employee.inActivedAt === '')).map(employee => employee.employeeId);

        if (_.size(FoundInactivatedDateForActiveWorker)) {
            return [400, {
                ok: false,
                message: `Found inactivated date for is_active = Yes. Please remove inactivated date value for following employee ids in the sheet. \n [ ${Array.from(_.uniq(FoundInactivatedDateForActiveWorker)).join('\n')} ] `
            }]
        }

        if (_.size(FoundInactivatedDateForBlankActiveFlag)) {
            return [400, {
                ok: false,
                message: `Found inactivated date for blank is_active. Please add is_active flag value for following employee ids in the sheet. \n [ ${Array.from(_.uniq(FoundInactivatedDateForBlankActiveFlag)).join('\n')} ] `
            }]
        }

        if (_.size(InactivatedDateNotFound)) {
            return [400, {
                ok: false,
                message: `Inactivated date not found for is_active = No. Please add inactivated date value for following employee ids in the sheet. \n [ ${Array.from(_.uniq(InactivatedDateNotFound)).join('\n')} ] `
            }]
        }
        // separate out workers who requested Job or Site Data Updation
        const { checkWorkersJobs, remainingWorkers } = existingWorkers.reduce((acc, worker) => {
            let existingWorkerDetails = availableEmployee.find(existing => existing.employeeId.toLowerCase() == worker.employeeId.toLowerCase() || existing.email.toLowerCase() == worker.email.toLowerCase());


            const { jobName, jobType, shiftName, departmentName, siteName, userId, firstName, lastName, workerId, pensionOptOut, isActive, employeeId, historicalEmployeeIds } = existingWorkerDetails || {};

            // Workers who requested to update Job or site data
            if (worker.siteName || worker.departmentName || worker.shiftName || worker.jobType || worker.jobName) {
                worker.jobName = worker.jobName || jobName;
                worker.jobType = worker.jobType || jobType;
                worker.shiftName = worker.shiftName || shiftName;
                worker.departmentName = worker.departmentName || departmentName;
                worker.siteName = worker.siteName || siteName;
                worker.userId = worker.userId || userId;
                worker.firstName = worker.firstName || firstName;
                worker.lastName = worker.lastName || lastName;
                worker.workerId = parseInt(workerId)
                worker.fromPensionOptOut = pensionOptOut
                worker.oldIsActive = isActive
                worker.historicalEmployeeIds = worker.isReturningWorker ? (!historicalEmployeeIds ? JSON.stringify([employeeId]) : JSON.stringify(JSON.parse(historicalEmployeeIds).concat(employeeId))) : null;
                acc.checkWorkersJobs.push(worker);
            } else { // Other than 'checkWorkersJobs' workers
                worker.userId = worker.userId || userId;
                worker.firstName = worker.firstName || firstName;
                worker.lastName = worker.lastName || lastName;
                worker.workerId = parseInt(workerId)
                worker.fromPensionOptOut = pensionOptOut
                worker.oldIsActive = isActive
                worker.historicalEmployeeIds = worker.isReturningWorker ? (!historicalEmployeeIds ? JSON.stringify([employeeId]) : JSON.stringify(JSON.parse(historicalEmployeeIds).concat(employeeId))) : null;
                acc.remainingWorkers.push(worker);
            }
            return acc;
        }, {
            checkWorkersJobs: [],   // Workers who requested to update Job or site data
            remainingWorkers: []    // Other than 'checkWorkersJobs' workers
        });

        // For 'checkWorkersJobs', check if requested job or site data valid as per the client and sites
        const jobNotFound = [];
        let associatedClientJobs = await getAllJobsByClientID(requestArgs.client_id);
        const updatedWorkers = checkWorkersJobs.map(worker => {
            const jobDetails = associatedClientJobs.find(
                job =>
                    job.job_name === worker.jobName.toLowerCase() &&
                    job.job_type === String(worker.jobType) &&
                    job.department_name === worker.departmentName.toLowerCase() &&
                    job.shift_name === worker.shiftName.toLowerCase() &&
                    job.site_name === worker.siteName.toLowerCase()
            );
            if (jobDetails) {
                worker.jobId = jobDetails.job_id;
                delete worker.jobName;
            } else {
                jobNotFound.push(`${worker.siteName} || ${worker.jobName} - ${worker.shiftName} - ${RoleType[worker.jobType]} - ${worker.departmentName}`);
            }
            return worker;
        });

        if (_.size(jobNotFound)) {
            return [400, {
                ok: false,
                message: `Invalid site or job combination. Please check the spelling and try again or contact the admin. \n [ ${Array.from(_.uniq(jobNotFound)).join('\n')} ] `
            }]
        }

        // Proceed to update users and workers data
        // For each eligible worker, merge in the needed fields from availableEmployee for compliance reset logic
        const availableEmployeeMap = new Map();
        availableEmployee.forEach(emp => { availableEmployeeMap.set(emp.workerId, emp); });

        const eligibleWorkersToUpdate = updatedWorkers.concat(remainingWorkers).map(worker => {
            const emp = availableEmployeeMap.get(String(worker.workerId));
            return {
                ...worker,
                oldAccountNumber: emp ? emp.accountNumber : null,
                oldSortCode: emp ? emp.sortCode : null,
                oldPostCode: emp ? emp.postCode : null,
                oldHouseNumber: emp ? emp.houseNumber : null
            };
        });
        const updatedAt = new Date()
        const entityManager = getManager(); // Use entity manager for the transaction

        // Start a transaction
        await entityManager.transaction(async (transactionalEntityManager) => {
            let workerIdsToSendInActiveMessage = [];

            // fetch all existing approvals for all relevant workers and cardIds (2, 6)
            const allWorkerIds = eligibleWorkersToUpdate.map(w => w.workerId);
            const allApprovals = await getComplianceApprovalForWorkersIfExists(allWorkerIds)

            // Build a map for quick lookup
            const approvalMap = new Map();
            allApprovals.forEach(a => {
                const approval = a as any;
                approvalMap.set(`${approval.workerId}_${approval.complianceCardId}`, approval);
            });
            // Array to collect all needed reset inserts
            const resetApprovalsToInsert = [];

            const updatePromises = eligibleWorkersToUpdate.map(async (worker) => {
                // User details update
                let dataToUpdateUser = {
                    updatedBy: userId,
                    updatedAt: updatedAt,
                };
                if (worker.nationalInsuranceNumber) dataToUpdateUser["nationalInsuranceNumber"] = worker.nationalInsuranceNumber;
                if (worker.email) dataToUpdateUser["email"] = worker.email;
                if (worker.firstName || worker.lastName) dataToUpdateUser["name"] = worker.firstName + ' ' + worker.lastName;

                // Use the transactionalEntityManager for the updateUser function
                await updateUserTransaction(transactionalEntityManager, worker.userId, dataToUpdateUser);

                // Worker details update (cleanedWorker)
                let cleanedWorker = Object.entries(worker).reduce((acc, [key, value]) => {
                    if (value) acc[key] = value;
                    return acc;
                }, Object({}));

                // Multiple Occupancy Compliance Approval Reset
                const isPostCodeOrHouseNumberChanged = (
                    (cleanedWorker.postCode && cleanedWorker.postCode !== worker.oldPostCode) ||
                    (cleanedWorker.houseNumber && cleanedWorker.houseNumber !== worker.oldHouseNumber)
                );

                if (isPostCodeOrHouseNumberChanged && approvalMap.has(`${worker.workerId}_${ComplinaceCardsType.MULTIPLE_OCCUPANCY}`)) {
                    resetApprovalsToInsert.push(buildResetApprovalPayload(ComplinaceCardsType.MULTIPLE_OCCUPANCY, worker, updatedAt, userId));
                }

                // Banking Detailes Compliance Approval Reset
                const isAccountNumberOrSortCodeChanged = (
                    (cleanedWorker.accountNumber && cleanedWorker.accountNumber !== worker.oldAccountNumber) ||
                    (cleanedWorker.sortCode && cleanedWorker.sortCode !== worker.oldSortCode)
                )
                if (isAccountNumberOrSortCodeChanged && approvalMap.has(`${worker.workerId}_${ComplinaceCardsType.BANKING}`)) {
                    resetApprovalsToInsert.push(buildResetApprovalPayload(ComplinaceCardsType.BANKING, worker, updatedAt, userId));
                }

                // Pension Opt Out handling
                if (cleanedWorker.pensionOptOut) {
                    let requestPayloadPensionOptOut = cleanedWorker.pensionOptOut.trim().toString().toLowerCase() === "yes";

                    if (worker.pensionOptOut !== requestPayloadPensionOptOut) {
                        const newPensionStatusLog = {
                            workerId: worker.workerId,
                            from: worker.fromPensionOptOut ? PensionStatus.OPTED_OUT : PensionStatus.OPTED_IN,
                            to: requestPayloadPensionOptOut ? PensionStatus.OPTED_OUT : PensionStatus.OPTED_IN,
                            createdBy: parseInt(userId),
                        };
                        // Use transactionalEntityManager for creating PensionStatusLog
                        await createPensionStatusLogTransaction(transactionalEntityManager, newPensionStatusLog);
                    }
                }

                // Further cleaning up worker details and other operations
                if (cleanedWorker.pensionOptOut) cleanedWorker.pensionOptOut = cleanedWorker.pensionOptOut.toString().toLowerCase() === "yes";
                if (cleanedWorker.transport) cleanedWorker.transport = cleanedWorker.transport.toString().toLowerCase() === "yes";
                if (cleanedWorker.otherAssignment) cleanedWorker.otherAssignment = cleanedWorker.otherAssignment.toString().toLowerCase() === "yes";
                if (cleanedWorker.studentVisa) cleanedWorker.studentVisa = cleanedWorker.studentVisa.toString().toLowerCase() === "yes";
                if (cleanedWorker.limitedHours) cleanedWorker.limitedHours = cleanedWorker.limitedHours.toString().toLowerCase() === "yes";

                if (cleanedWorker.isActive) {
                    cleanedWorker.isActive = cleanedWorker.isActive.trim().toLowerCase() === "yes";
                    if (!cleanedWorker.isActive && cleanedWorker.oldIsActive) {
                        workerIdsToSendInActiveMessage.push(cleanedWorker.oldIsActive);
                        cleanedWorker.inactivatedBy = userId;
                    } else {
                        cleanedWorker.inActivedAt = null;
                        cleanedWorker.inactivatedBy = null;
                    }
                }

                cleanedWorker['updatedBy'] = userId;
                cleanedWorker['updatedAt'] = updatedAt;

                // Remove unnecessary keys for the workers table
                delete cleanedWorker.email;
                delete cleanedWorker.departmentName;
                delete cleanedWorker.shiftName;
                delete cleanedWorker.roleType;
                delete cleanedWorker.roleTypeName;
                delete cleanedWorker.jobType;
                delete cleanedWorker.siteName;
                delete cleanedWorker.index;
                delete cleanedWorker.workerId;
                delete cleanedWorker.oldIsActive;
                delete cleanedWorker.fromPensionOptOut;
                delete cleanedWorker.oldAccountNumber;
                delete cleanedWorker.oldSortCode;
                delete cleanedWorker.oldPostCode;
                delete cleanedWorker.oldHouseNumber;

                // Update the worker details using transactionalEntityManager
                await updateWorkersDataTransaction(transactionalEntityManager, cleanedWorker, worker.workerId);
            });

            // Wait for all database update promises to complete
            await Promise.all(updatePromises);
            // Bulk insert all needed ComplianceApproval resets
            if (resetApprovalsToInsert.length) {
                await insertComplianceApprovalTx(transactionalEntityManager, resetApprovalsToInsert);
            }

            // Handle inactive worker notifications if needed
            if (workerIdsToSendInActiveMessage.length && config.SEND_WORKERS_MOBILE_INACTIVITY_NOTIFICATION === 1) {
                await sendUnassignedWorkerMessages(workerIdsToSendInActiveMessage);
            }
        });


        if (employeeNotFound.length) {
            const errorObj = await dynamicErrorObject(ErrorResponse.WorkersNotUpdated, employeeNotFound.join(', '))
            return [400, errorObj]
        }

        return [200, {
            'ok': true,
            message: MessageActions.UPDATE_WORKERS,
            workersToAdd: employeeNotFound
        }]
    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError && err.sqlMessage.includes("email_UNIQUE")) {
            return [409, ErrorResponse.EmailAlreadyExists]    // Return 409 if Email already exists in db
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [422, ErrorResponse.UnprocessableEntity]    // return [422, ErrorResponse.UnprocessableEntity]
        }
    }
};

const validateWorkerUserEmail = async (emailList, userId) => {
    let { userData, existingWorkerUser, individualworkerUser } = await getRequestedUserEmail(emailList, userId);
    let existingWorkerUserEmailList = existingWorkerUser.map(worker => worker.email);
    let individualworkerUserEmailList = individualworkerUser.map(worker => worker.email);
    const mergedWorkerUserEmailList = [...existingWorkerUserEmailList, ...individualworkerUserEmailList];      // merge individualworkerUserEmailList into existingWorkerUserEmailList

    const emailSet = new Set(mergedWorkerUserEmailList);
    const existingUserEmailList = userData.reduce((result, worker) => {
        if (!emailSet.has(worker.email)) {
            result.push(worker.email);
            emailSet.add(worker.email);
        }
        return result;
    }, []);

    existingWorkerUser = existingWorkerUser.map((workerData) => {
        return `[ email: ${workerData.email}, site name: ${workerData.site_name} ]`;
    });

    return { existingUserEmailList, existingWorkerUser, individualworkerUser }
}


export const checkAndAddAgencyId = async (clientId, workerPerformanceData) => {
    const agencies = await getAgencyAssociationByClientId(clientId);
    const agencyMap = new Map(agencies.map(agency => [agency.agency_name.toLowerCase(), agency.agency_id]));

    let agencyNotFound = []
    const checkedAgencies = new Set();
    for (const wkr of workerPerformanceData) {
        if (!checkedAgencies.has(wkr.agency_name)) {
            if (!agencyMap.has(wkr.agency_name)) {
                agencyNotFound.push(wkr.agency_name);
            }
            checkedAgencies.add(wkr.agency_name);
        }
        wkr.agency_id = agencyMap.get(wkr.agency_name);
    }
    return [workerPerformanceData, agencyNotFound];
};

/**
 * Service to add worker performance
 */
export const addWorkerPerformanceService = async (fileContent, workerPerformanceData, payload, loggedInUser) => {
    let workerPerformance: any = {};
    try {

        const { client_id: clientId, site_id: siteId, start_date, end_date, week, isClientUser } = payload;
        const siteToUpdate = await getSiteById(siteId);

        if (!siteToUpdate || siteToUpdate.client_id != clientId) {
            return [404, ErrorResponse.ResourceNotFound];
        }

        const userTypeId = parseInt(loggedInUser.user_type_id);
        const userDetails = await getAllUsers(loggedInUser);

        const permissionDenied = () => [403, ErrorResponse.PermissionDenied];
        const clientMismatch = loggedInUser.client_id != clientId;
        const regionMismatch = userDetails.region_id != siteToUpdate.region_id;
        const siteMismatch = userDetails.site_id != siteId;
        if ([UserType.CLIENT_ADMIN].includes(userTypeId) && clientMismatch) return permissionDenied();
        if ([UserType.AGENCY_ADMIN].includes(userTypeId)) {
            const existingAssociation = await getAgencyAssociationByAgencyIdAndClientId(userDetails.agency_id, clientId);
            if (!existingAssociation) return permissionDenied();
        } else if ([UserType.CLIENT_REGIONAL, UserType.AGENCY_REGIONAL].includes(userTypeId) && (clientMismatch || regionMismatch)) {
            return permissionDenied();
        } else if ([UserType.CLIENT_SITE, UserType.AGENCY_SITE].includes(userTypeId) && siteMismatch) {
            return permissionDenied();
        }

        let agencyNotFound = [];
        if ([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE].includes(userTypeId)) {
            workerPerformanceData.forEach(wkr => wkr.agency_id = userDetails.agency_id);
        } else {
            [workerPerformanceData, agencyNotFound] = await checkAndAddAgencyId(clientId, workerPerformanceData);
        }

        if (agencyNotFound.length > 0) {
            return [400, {
                status: 400,
                ok: false,
                message: `Following agencies not found: \n [ ${agencyNotFound.join(', ')} ]`,
                error: "AGENCY_NAME_NOT_FOUND"
            }];
        }

        if (diffBetweenGivenTwoDatesInDays(start_date, end_date) != 6) {
            return [400, ErrorResponse.WrongDateRange];
        }

        const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][new Date(start_date).getDay()];
        const clientDetails = await getClientsById(clientId);
        if (!clientDetails || clientDetails.weekday_start != dayName) {
            return [400, ErrorResponse.InvalidStartDate];
        }

        const exisitingWorkerPerformance = await getWorkerPerformance({ startDate: start_date, endDate: end_date, clientId, siteId });
        if (exisitingWorkerPerformance.length > 0) {
            const clientsUpload = exisitingWorkerPerformance.filter(({ uploadedBy }) => uploadedBy == "CLIENT");
            if (clientsUpload.length > 0) {
                return [409, ErrorResponse.WorkerPerformanceAlreadyExists];
            } else if (isClientUser) {
                return [409, {
                    status: 409,
                    ok: false,
                    message: "Data for this week already uploaded by associated agencies. Please delete the existing data and then re-upload.",
                    error: "WORKER_PERFORMANCE_FILE_ALREADY_EXISTS"
                }];
            } else {
                const currentAgencyUploads = exisitingWorkerPerformance.filter(({ agencyId, uploadedBy }) => agencyId == `${userDetails.agency_id}` && uploadedBy == "AGENCY");
                if (currentAgencyUploads.length > 0) {
                    return [409, ErrorResponse.WorkerPerformanceAlreadyExists];
                }
            }
        }

        const exisitingTimeAndAttendance = await getTimeAndAttendance({
            startDate: start_date,
            endDate: end_date,
            clientId,
            siteId,
            ...(isClientUser ? {} : { agencyId: userDetails.agency_id })
        });

        if (exisitingTimeAndAttendance) {
            return [409, {
                status: 409,
                ok: false,
                message: "Time and attendance data for the week has been found. Please delete them and re-upload the worker performance.",
                error: "TNA_EXISTS_FOR_THE_WEEK"
            }];
        }

        const filename = uuid.v4();
        const url = await uploadFileOnS3(config.BUCKET_NAME, config.WORKER_PERFORMANCE_FOLDER, filename, "csv", fileContent);
        const workerPerformanceMetaData = {
            awsPath: url.location,
            filename,
            week,
            status: TimeAndAttendanceStatus.PROCESSED,
            clientId,
            agencyId: isClientUser ? null : userDetails.agency_id,
            uploadedBy: isClientUser ? "CLIENT" : "AGENCY",
            siteId,
            startDate: start_date,
            endDate: end_date,
            createdBy: loggedInUser.user_id,
            updatedBy: loggedInUser.user_id,
        };

        workerPerformance = await createWorkerPerformance(workerPerformanceMetaData);
        workerPerformance = await getWorkerPerformanceById(workerPerformance.id);

        const { workers } = await EasyPromiseAll({
            workers: getWorkersByEmployeeIdAndAgencyIds(_.map(workerPerformanceData, 'employee_id').filter(Boolean), _.map(workerPerformanceData, 'agency_id').filter(Boolean))
        });

        const workerNotFoundEmployeeId = [];
        workerPerformanceData.forEach(wkr => {
            const worker = _.find(workers, { employee_id: wkr.employee_id, agency_id: wkr.agency_id });
            if (worker) {
                wkr.workerId = worker.id;
            } else {
                workerNotFoundEmployeeId.push(wkr.employee_id || "''");
            }
            Object.assign(wkr, {
                employeeId: wkr.employee_id,
                clientId,
                agencyId: wkr.agency_id,
                siteId,
                createdBy: loggedInUser.user_id,
                updatedBy: loggedInUser.user_id,
                workerPerformanceId: workerPerformance.id,
                performanceNumber: wkr.performance_number,
                week,
                startDate: start_date,
                endDate: end_date
            });
        });

        if (workerNotFoundEmployeeId.length) {
            if (workerPerformance.id) {
                await deleteObject(config.BUCKET_NAME, config.WORKER_PERFORMANCE_FOLDER, workerPerformance.filename);
                await deleteWorkerPerformanceById(workerPerformance.id);
            }
            return [400, {
                ok: false,
                message: `worker id does not match for employee id(s): (${workerNotFoundEmployeeId})`
            }];
        }

        await deleteWorkerPerformanceDataById(workerPerformance.id);

        const arrays = await splitArrayWithSize(workerPerformanceData, 1000);
        const queryRunner = getNewTransaction();
        await queryRunner.startTransaction();

        try {
            for (const chunk of arrays) {
                await addWorkerPerformanceData(chunk);
            }
            await queryRunner.commitTransaction();
        } catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        } finally {
            await queryRunner.release();
        }

        return [201, {
            'ok': true,
            message: MessageActions.CREATE_WORKER_PERFORMANCE_FOLDER + (!clientDetails.worker_performance ? ` However, the client's workers' performance checks are disabled, so it won't verify the employee's performance level for pay and charge rates for 'time and attendance uploads'.` : "")
        }]
    } catch (err) {
        if (workerPerformance.id) {
            await deleteObject(config.BUCKET_NAME, config.WORKER_PERFORMANCE_FOLDER, workerPerformance.filename);
            await deleteWorkerPerformanceDataById(workerPerformance.id);
            await deleteWorkerPerformanceById(workerPerformance.id);
        }

        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else if (ErrorCodes.wrongValueForField.includes(err.code)) {
            return [422, ErrorResponse.UnprocessableEntityForFile]
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
};


/**
 * Service to GET Worker Performance Sheet.
 */
export const downloadWorkerPerformanceFileService = async (loggedInUser, params) => {

    const exisitingWorkerPerformance = await getWorkerPerformance({
        startDate: params.start_date,
        endDate: params.end_date,
        clientId: params.client_id,
        siteId: params.site_id,
    });

    if (!exisitingWorkerPerformance.length) {
        return [404, ErrorResponse.WorkerPerformanceFileNotFound];
    }

    const isClientUser = params.isClientUser;
    const fileDetails = exisitingWorkerPerformance.filter(({ uploadedBy }) =>
        (isClientUser && uploadedBy === "CLIENT") || (!isClientUser && uploadedBy === "AGENCY")
    );

    if (!fileDetails.length) {
        const errorObj = await dynamicErrorObject(ErrorResponse.InvalidWorkerPerformanceDownloadRequest, isClientUser ? "agencies" : "client")
        return [400, errorObj];
    }

    const files = await Promise.all(fileDetails.map(async item => {
        const link = await getSignedUrlForGetObject(config.BUCKET_NAME, config.WORKER_PERFORMANCE_FOLDER, item.filename);
        return { resource_url: link.url, worker_performance_id: item.id };
    }));


    return [200, {
        ok: true,
        files_to_download: files
    }];
};


/**
 * Service to delete Worker Performance Data.
 */
export const deleteWorkerPerformanceDataService = async (payload, loggedInUser) => {
    try {
        let userDetails = await getUserById(loggedInUser.user_id);

        if ((payload.isClientUser && loggedInUser.client_id != payload.client_id) ||
            (!payload.isClientUser && !(await getAgencyAssociationByAgencyIdAndClientId(userDetails.agency_id, payload.client_id)))) {
            return [403, ErrorResponse.PermissionDenied];
        }

        let siteDetails: any = await getAllSites(payload.client_id);
        let site_id_list = siteDetails[1].sites.map(object => parseInt(object.id));
        if (!site_id_list.includes(payload.site_id)) {
            return [403, ErrorResponse.PermissionDenied];
        }

        const exisitingWorkerPerformance = await getWorkerPerformance({
            startDate: payload.start_date,
            endDate: payload.end_date,
            clientId: payload.client_id,
            siteId: payload.site_id,
        });

        if (!exisitingWorkerPerformance.length) {
            return [404, ErrorResponse.WorkerPerformanceFileNotFound];
        }

        let workerPerformanceToDelete = [];
        const clientsUpload = exisitingWorkerPerformance.filter(({ uploadedBy }) => uploadedBy == "CLIENT");
        const agencyUpload = exisitingWorkerPerformance.filter(({ uploadedBy }) => uploadedBy == "AGENCY");

        if (payload.isClientUser) {
            // Client User & WP Uploaded By Client OR All Agencies
            workerPerformanceToDelete = exisitingWorkerPerformance;
        } else if (clientsUpload.length > 0) {
            // Agency User & WP Uploaded By Client
            return [400, ErrorResponse.InvalidWorkerPerformanceDeleteRequest];
        } else {
            // Agency User & WP Uploaded By Agencies
            workerPerformanceToDelete = agencyUpload.filter(({ agencyId }) => agencyId == `${userDetails.agency_id}`);
            if (!workerPerformanceToDelete.length) {
                return [404, ErrorResponse.WorkerPerformanceFileNotFound];
            }
        }

        await Promise.all(workerPerformanceToDelete.map(async item => {
            await deleteObject(config.BUCKET_NAME, config.WORKER_PERFORMANCE_FOLDER, item.filename);
            await deleteWorkerPerformanceDataHelper(item.id);
        }));

        await deletePayrollData({
            start_date: payload.start_date,
            client_id: payload.client_id,
            site_id: payload.site_id,
            ...(payload.isClientUser ? {} : { agency_id: userDetails.agency_id })
        }, loggedInUser);

        return [200, {
            ok: true,
            message: MessageActions.DELETE_WORKER_PERFORMANCE,
        }];

    } catch (err) {
        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}